"""HTTP client for Flowable Platform API - Optimized for High Concurrency"""

import asyncio
import httpx
import logging
import base64
from typing import Dict, Any, Optional, Union
import json
from pathlib import Path
from contextlib import asynccontextmanager
import threading
from weakref import WeakSet

from config import BASE_URL, DEFAULT_HEADERS, <PERSON><PERSON><PERSON><PERSON>ART_HEADERS, FLOWABLE_USERNAME, FLOW<PERSON>LE_PASSWORD, FLOWABLE_API_TOKEN, AUTH_TYPES
from models import FlowableResponse, APIError, AuthType

logger = logging.getLogger("flowable-mcp-server")


class ConnectionPool:
    """Manages HTTP client connection pool for high concurrency"""
    
    def __init__(self, max_clients: int = 50):
        self.max_clients = max_clients
        self.available_clients = None
        self.active_clients = WeakSet()
        self._lock = asyncio.Lock()
        self._initialized = False
        self.base_url = BASE_URL
    
    async def initialize(self):
        """Initialize the connection pool"""
        if self._initialized:
            return
            
        async with self._lock:
            if self._initialized:
                return
                
            self.available_clients = asyncio.Queue(maxsize=self.max_clients)
            
            # Pre-create HTTP clients with optimized settings
            for _ in range(self.max_clients):
                client = httpx.AsyncClient(
                    timeout=httpx.Timeout(
                        60.0,           # Total timeout
                        connect=15.0,   # Connection timeout
                        read=45.0,      # Read timeout
                        write=15.0,     # Write timeout
                        pool=15.0       # Pool timeout
                    ),
                    limits=httpx.Limits(
                        max_keepalive_connections=100,  # Increased for concurrency
                        max_connections=200,            # Much higher limit
                        keepalive_expiry=120.0          # Longer keepalive
                    ),
                    verify=True,
                    trust_env=True,
                    follow_redirects=True,
                    # Additional optimizations
                    http2=True  # Enable HTTP/2 for better performance
                )
                await self.available_clients.put(client)
            
            self._initialized = True
            logger.info(f"Connection pool initialized with {self.max_clients} clients")
    
    @asynccontextmanager
    async def get_client(self):
        """Get an HTTP client from the pool with fallback"""
        if not self._initialized:
            await self.initialize()
            
        client = None
        is_pool_client = False
        
        try:
            # Try to get client from pool with short timeout
            try:
                client = await asyncio.wait_for(
                    self.available_clients.get(), 
                    timeout=2.0
                )
                is_pool_client = True
                self.active_clients.add(client)
            except asyncio.TimeoutError:
                # Create temporary client if pool is exhausted
                logger.warning("Connection pool exhausted, creating temporary client")
                client = self._create_temp_client()
                is_pool_client = False
            
            yield client
            
        finally:
            # Return client to pool if it's a pool client and still valid
            if client and is_pool_client and not client.is_closed:
                try:
                    self.active_clients.discard(client)
                    await self.available_clients.put(client)
                except Exception as e:
                    logger.warning(f"Error returning client to pool: {e}")
                    await client.aclose()
            elif client and not is_pool_client:
                # Close temporary client
                await client.aclose()
    
    def _create_temp_client(self) -> httpx.AsyncClient:
        """Create a temporary HTTP client with same settings"""
        return httpx.AsyncClient(
            timeout=httpx.Timeout(60.0, connect=15.0, read=45.0, write=15.0, pool=15.0),
            limits=httpx.Limits(max_keepalive_connections=20, max_connections=50, keepalive_expiry=60.0),
            verify=True,
            trust_env=True,
            follow_redirects=True,
            http2=True
        )
    
    async def close_all(self):
        """Close all clients in the pool"""
        if not self._initialized:
            return
            
        async with self._lock:
            # Close all active clients
            for client in list(self.active_clients):
                try:
                    await client.aclose()
                except Exception as e:
                    logger.warning(f"Error closing active client: {e}")
            
            # Close all available clients
            while not self.available_clients.empty():
                try:
                    client = await self.available_clients.get()
                    await client.aclose()
                except Exception as e:
                    logger.warning(f"Error closing pooled client: {e}")
            
            self._initialized = False
            logger.info("All HTTP clients closed")


# Global connection pool
connection_pool = ConnectionPool()


class FlowableClient:
    """High-performance HTTP client for Flowable Platform API"""
    
    def __init__(self, base_url: str = None, username: str = None, password: str = None, api_token: str = None):
        self.base_url = base_url or BASE_URL
        self.username = username or FLOWABLE_USERNAME
        self.password = password or FLOWABLE_PASSWORD
        self.api_token = api_token or FLOWABLE_API_TOKEN
        self.session_active = False
        
        # Determine authentication method
        if self.api_token:
            self.auth_type = AuthType.TOKEN
        elif self.username and self.password:
            self.auth_type = AuthType.BASIC
        else:
            self.auth_type = None
            logger.warning("No authentication credentials provided")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await connection_pool.initialize()
        self.session_active = True
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        self.session_active = False
    
    async def close(self):
        """Close the client and cleanup resources"""
        self.session_active = False
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers based on auth type"""
        headers = {}
        
        if self.auth_type == AuthType.BASIC and self.username and self.password:
            # Basic authentication
            credentials = f"{self.username}:{self.password}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            headers["Authorization"] = f"Basic {encoded_credentials}"
        elif self.auth_type == AuthType.TOKEN and self.api_token:
            # Token authentication (Bearer token)
            headers["Authorization"] = f"Bearer {self.api_token}"
        
        return headers
    
    def _build_url(self, endpoint: str, **path_params) -> str:
        """Build full URL with path parameters"""
        # Replace path parameters in endpoint
        formatted_endpoint = endpoint.format(**path_params)
        
        # Ensure endpoint starts with /
        if not formatted_endpoint.startswith('/'):
            formatted_endpoint = '/' + formatted_endpoint
        
        # Remove trailing slash from base_url if present
        base = self.base_url.rstrip('/')
        
        return f"{base}{formatted_endpoint}"
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        files: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **path_params
    ) -> FlowableResponse:
        """Make HTTP request with error handling and retries"""
        
        if not self.session_active:
            raise APIError("Client session not active. Use async context manager.")
        
        url = self._build_url(endpoint, **path_params)
        
        # Prepare headers
        request_headers = DEFAULT_HEADERS.copy()
        request_headers.update(self._get_auth_headers())
        if headers:
            request_headers.update(headers)
        
        # Prepare request data
        json_data = None
        form_data = None
        
        if files:
            # Multipart form data
            form_data = data or {}
            # Remove Content-Type for multipart - httpx will set it
            request_headers.pop("Content-Type", None)
        elif data:
            json_data = data
        
        logger.debug(f"Making {method} request to {url}")
        
        try:
            async with connection_pool.get_client() as client:
                response = await client.request(
                    method=method,
                    url=url,
                    json=json_data,
                    data=form_data,
                    files=files,
                    params=params,
                    headers=request_headers
                )
                
                # Parse response
                response_data = None
                try:
                    if response.content:
                        response_data = response.json()
                except (json.JSONDecodeError, ValueError):
                    # Handle non-JSON responses
                    response_data = {"content": response.text}
                
                # Check for HTTP errors
                if response.status_code >= 400:
                    error_message = f"HTTP {response.status_code}: {response.reason_phrase}"
                    if response_data and isinstance(response_data, dict):
                        if "message" in response_data:
                            error_message = response_data["message"]
                        elif "error" in response_data:
                            error_message = response_data["error"]
                    
                    return FlowableResponse(
                        success=False,
                        data=response_data,
                        status_code=response.status_code,
                        error=error_message
                    )
                
                # Success response
                return FlowableResponse(
                    success=True,
                    data=response_data,
                    status_code=response.status_code,
                    message="Request successful"
                )
                
        except httpx.TimeoutException as e:
            logger.error(f"Request timeout for {method} {url}: {str(e)}")
            return FlowableResponse(
                success=False,
                error=f"Request timeout: {str(e)}",
                status_code=None
            )
        except httpx.ConnectError as e:
            logger.error(f"Connection error for {method} {url}: {str(e)}")
            return FlowableResponse(
                success=False,
                error=f"Connection error: {str(e)}",
                status_code=None
            )
        except Exception as e:
            logger.error(f"Unexpected error for {method} {url}: {str(e)}")
            return FlowableResponse(
                success=False,
                error=f"Unexpected error: {str(e)}",
                status_code=None
            )
    
    async def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None, **path_params) -> FlowableResponse:
        """Make GET request"""
        return await self._make_request("GET", endpoint, params=params, **path_params)
    
    async def post_json(self, endpoint: str, data: Optional[Dict[str, Any]] = None, **path_params) -> FlowableResponse:
        """Make POST request with JSON data"""
        return await self._make_request("POST", endpoint, data=data, **path_params)
    
    async def post_form(self, endpoint: str, files: Optional[Dict[str, Any]] = None, 
                       data: Optional[Dict[str, Any]] = None, **path_params) -> FlowableResponse:
        """Make POST request with form data/files"""
        return await self._make_request("POST", endpoint, data=data, files=files, **path_params)
    
    async def put(self, endpoint: str, data: Optional[Dict[str, Any]] = None, **path_params) -> FlowableResponse:
        """Make PUT request"""
        return await self._make_request("PUT", endpoint, data=data, **path_params)
    
    async def delete(self, endpoint: str, params: Optional[Dict[str, Any]] = None, **path_params) -> FlowableResponse:
        """Make DELETE request"""
        return await self._make_request("DELETE", endpoint, params=params, **path_params)
    
    async def patch(self, endpoint: str, data: Optional[Dict[str, Any]] = None, **path_params) -> FlowableResponse:
        """Make PATCH request"""
        return await self._make_request("PATCH", endpoint, data=data, **path_params)
