"""Configuration for Flowable Open Source MCP Server"""

import os
from typing import Optional

# Flowable Open Source API Configuration
# Base URLs for different Flowable Open Source APIs
PROCESS_API_BASE_URL = os.getenv("FLOWABLE_PROCESS_API_BASE_URL", "http://localhost:8080/flowable-rest/service")
DMN_API_BASE_URL = os.getenv("FLOWABLE_DMN_API_BASE_URL", "http://localhost:8080/flowable-rest/service")
CMMN_API_BASE_URL = os.getenv("FLOWABLE_CMMN_API_BASE_URL", "http://localhost:8080/flowable-rest/service")
FORM_API_BASE_URL = os.getenv("FLOWABLE_FORM_API_BASE_URL", "http://localhost:8080/flowable-rest/service")
CONTENT_API_BASE_URL = os.getenv("FLOWABLE_CONTENT_API_BASE_URL", "http://localhost:8080/flowable-rest/service")

# Default base URL (for backward compatibility)
BASE_URL = os.getenv("FLOWABLE_BASE_URL", PROCESS_API_BASE_URL)

# Environment variables for authentication
FLOWABLE_USERNAME: Optional[str] = os.getenv("FLOWABLE_USERNAME", "admin")
FLOWABLE_PASSWORD: Optional[str] = os.getenv("FLOWABLE_PASSWORD", "test")
FLOWABLE_API_TOKEN: Optional[str] = os.getenv("FLOWABLE_API_TOKEN")

# API Endpoints based on Flowable Open Source REST API
ENDPOINTS = {
    # Repository - Deployments
    "deployments": "/repository/deployments",
    "deployment": "/repository/deployments/{deploymentId}",
    "deployment_resources": "/repository/deployments/{deploymentId}/resources",
    "deployment_resource": "/repository/deployments/{deploymentId}/resources/{resourceId}",
    "deployment_resource_data": "/repository/deployments/{deploymentId}/resourcedata/{resourceId}",

    # Repository - Process Definitions
    "process_definitions": "/repository/process-definitions",
    "process_definition": "/repository/process-definitions/{processDefinitionId}",
    "process_definition_resource": "/repository/process-definitions/{processDefinitionId}/resourcedata",
    "process_definition_model": "/repository/process-definitions/{processDefinitionId}/model",
    "process_definition_identity_links": "/repository/process-definitions/{processDefinitionId}/identitylinks",
    "process_definition_identity_link": "/repository/process-definitions/{processDefinitionId}/identitylinks/{family}/{identityId}",

    # Repository - Models
    "models": "/repository/models",
    "model": "/repository/models/{modelId}",
    "model_source": "/repository/models/{modelId}/source",
    "model_source_extra": "/repository/models/{modelId}/source-extra",

    # Runtime - Process Instances
    "process_instances": "/runtime/process-instances",
    "process_instance": "/runtime/process-instances/{processInstanceId}",
    "process_instance_diagram": "/runtime/process-instances/{processInstanceId}/diagram",
    "process_instance_involved_people": "/runtime/process-instances/{processInstanceId}/identitylinks",
    "process_instance_involved_person": "/runtime/process-instances/{processInstanceId}/identitylinks/{family}/{identityId}/{type}",
    "process_instance_variables": "/runtime/process-instances/{processInstanceId}/variables",
    "process_instance_variable": "/runtime/process-instances/{processInstanceId}/variables/{variableName}",

    # Runtime - Executions
    "executions": "/runtime/executions",
    "execution": "/runtime/executions/{executionId}",
    "execution_activities": "/runtime/executions/{executionId}/activities",
    "execution_variables": "/runtime/executions/{executionId}/variables",
    "execution_variable": "/runtime/executions/{executionId}/variables/{variableName}",

    # Runtime - Tasks
    "tasks": "/runtime/tasks",
    "task": "/runtime/tasks/{taskId}",
    "task_variables": "/runtime/tasks/{taskId}/variables",
    "task_variable": "/runtime/tasks/{taskId}/variables/{variableName}",
    "task_identity_links": "/runtime/tasks/{taskId}/identitylinks",
    "task_identity_link": "/runtime/tasks/{taskId}/identitylinks/{family}/{identityId}/{type}",
    "task_comments": "/runtime/tasks/{taskId}/comments",
    "task_comment": "/runtime/tasks/{taskId}/comments/{commentId}",
    "task_events": "/runtime/tasks/{taskId}/events",
    "task_event": "/runtime/tasks/{taskId}/events/{eventId}",
    "task_attachments": "/runtime/tasks/{taskId}/attachments",
    "task_attachment": "/runtime/tasks/{taskId}/attachments/{attachmentId}",
    "task_attachment_content": "/runtime/tasks/{taskId}/attachments/{attachmentId}/content",

    # History - Historic Process Instances
    "historic_process_instances": "/history/historic-process-instances",
    "historic_process_instance": "/history/historic-process-instances/{historicProcessInstanceId}",
    "historic_process_instance_identity_links": "/history/historic-process-instances/{historicProcessInstanceId}/identitylinks",
    "historic_process_instance_variables": "/history/historic-process-instances/{historicProcessInstanceId}/variables",
    "historic_process_instance_variable_data": "/history/historic-process-instances/{historicProcessInstanceId}/variables/{variableName}/data",
    "historic_process_instance_comments": "/history/historic-process-instances/{historicProcessInstanceId}/comments",
    "historic_process_instance_comment": "/history/historic-process-instances/{historicProcessInstanceId}/comments/{commentId}",

    # History - Historic Task Instances
    "historic_task_instances": "/history/historic-task-instances",
    "historic_task_instance": "/history/historic-task-instances/{historicTaskInstanceId}",
    "historic_task_instance_identity_links": "/history/historic-task-instances/{historicTaskInstanceId}/identitylinks",
    "historic_task_instance_variables": "/history/historic-task-instances/{historicTaskInstanceId}/variables",
    "historic_task_instance_variable_data": "/history/historic-task-instances/{historicTaskInstanceId}/variables/{variableName}/data",

    # History - Historic Activity Instances
    "historic_activity_instances": "/history/historic-activity-instances",

    # History - Historic Variable Instances
    "historic_variable_instances": "/history/historic-variable-instances",

    # History - Historic Details
    "historic_details": "/history/historic-details",
    "historic_detail_variable_data": "/history/historic-details/{historicDetailId}/data",

    # Forms
    "form_data": "/form/form-data",
    "submit_task_form": "/form/task-form-data",

    # Database Tables
    "database_tables": "/management/tables",
    "database_table": "/management/tables/{tableName}",
    "database_table_columns": "/management/tables/{tableName}/columns",
    "database_table_data": "/management/tables/{tableName}/data",

    # Engine Info
    "engine_properties": "/management/properties",
    "engine_info": "/management/engine",

    # Runtime - Signal Events
    "signal_event_received": "/runtime/signals",

    # Management - Jobs
    "jobs": "/management/jobs",
    "job": "/management/jobs/{jobId}",
    "job_exception_stacktrace": "/management/jobs/{jobId}/exception-stacktrace",

    # Management - Dead Letter Jobs
    "deadletter_jobs": "/management/deadletter-jobs",
    "deadletter_job": "/management/deadletter-jobs/{jobId}",
    "deadletter_job_exception_stacktrace": "/management/deadletter-jobs/{jobId}/exception-stacktrace",

    # Identity - Users
    "users": "/identity/users",
    "user": "/identity/users/{userId}",
    "user_picture": "/identity/users/{userId}/picture",
    "user_info": "/identity/users/{userId}/info",
    "user_info_key": "/identity/users/{userId}/info/{key}",

    # Identity - Groups
    "groups": "/identity/groups",
    "group": "/identity/groups/{groupId}",
    "group_members": "/identity/groups/{groupId}/members",
    "group_member": "/identity/groups/{groupId}/members/{userId}",
}

# API Base URLs for different engines (Open Source)
API_BASES = {
    "process": PROCESS_API_BASE_URL,
    "dmn": DMN_API_BASE_URL,
    "cmmn": CMMN_API_BASE_URL,
    "form": FORM_API_BASE_URL,
    "content": CONTENT_API_BASE_URL,
}

# Endpoint to API mapping (which API base to use for each endpoint)
ENDPOINT_API_MAPPING = {
    # Repository endpoints use process API
    "deployments": "process",
    "deployment": "process",
    "deployment_resources": "process",
    "deployment_resource": "process",
    "deployment_resource_data": "process",
    "process_definitions": "process",
    "process_definition": "process",
    "process_definition_resource": "process",
    "process_definition_model": "process",
    "process_definition_identity_links": "process",
    "process_definition_identity_link": "process",
    "models": "process",
    "model": "process",
    "model_source": "process",
    "model_source_extra": "process",

    # Runtime endpoints use process API
    "process_instances": "process",
    "process_instance": "process",
    "process_instance_diagram": "process",
    "process_instance_involved_people": "process",
    "process_instance_involved_person": "process",
    "process_instance_variables": "process",
    "process_instance_variable": "process",
    "executions": "process",
    "execution": "process",
    "execution_activities": "process",
    "execution_variables": "process",
    "execution_variable": "process",
    "tasks": "process",
    "task": "process",
    "task_variables": "process",
    "task_variable": "process",
    "task_identity_links": "process",
    "task_identity_link": "process",
    "task_comments": "process",
    "task_comment": "process",
    "task_events": "process",
    "task_event": "process",
    "task_attachments": "process",
    "task_attachment": "process",
    "task_attachment_content": "process",
    "signal_event_received": "process",

    # History endpoints use process API
    "historic_process_instances": "process",
    "historic_process_instance": "process",
    "historic_process_instance_identity_links": "process",
    "historic_process_instance_variables": "process",
    "historic_process_instance_variable_data": "process",
    "historic_process_instance_comments": "process",
    "historic_process_instance_comment": "process",
    "historic_task_instances": "process",
    "historic_task_instance": "process",
    "historic_task_instance_identity_links": "process",
    "historic_task_instance_variables": "process",
    "historic_task_instance_variable_data": "process",
    "historic_activity_instances": "process",
    "historic_variable_instances": "process",
    "historic_details": "process",
    "historic_detail_variable_data": "process",

    # Form endpoints use form API
    "form_data": "form",
    "submit_task_form": "form",

    # Management endpoints use process API
    "database_tables": "process",
    "database_table": "process",
    "database_table_columns": "process",
    "database_table_data": "process",
    "engine_properties": "process",
    "engine_info": "process",
    "jobs": "process",
    "job": "process",
    "job_exception_stacktrace": "process",
    "deadletter_jobs": "process",
    "deadletter_job": "process",
    "deadletter_job_exception_stacktrace": "process",

    # Identity endpoints use process API
    "users": "process",
    "user": "process",
    "user_picture": "process",
    "user_info": "process",
    "user_info_key": "process",
    "groups": "process",
    "group": "process",
    "group_members": "process",
    "group_member": "process",
}

# Default headers
DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

# File upload headers
MULTIPART_HEADERS = {
    # Content-Type will be set automatically for multipart/form-data
}

# Authentication types
AUTH_TYPES = {
    "BASIC": "basic",
    "TOKEN": "token"
}
