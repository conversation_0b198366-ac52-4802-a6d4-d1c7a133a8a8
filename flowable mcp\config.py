"""Configuration for Flowable MCP Server"""

import os
from typing import Optional

# Flowable API Configuration
BASE_URL = os.getenv("FLOWABLE_BASE_URL", "http://localhost:8090/platform-api")

# Environment variables for authentication
FLOWABLE_USERNAME: Optional[str] = os.getenv("FLOWABLE_USERNAME")
FLOWABLE_PASSWORD: Optional[str] = os.getenv("FLOWABLE_PASSWORD")
FLOWABLE_API_TOKEN: Optional[str] = os.getenv("FLOWABLE_API_TOKEN")

# API Endpoints based on Flowable Platform REST API
ENDPOINTS = {
    # Application Info
    "about_info": "/about-info",
    
    # Action Definitions
    "action_definitions_form_fields": "/action-definitions/{actionDefinitionId}/form/fields/{fieldId}/document-definitions",
    "action_definitions_invoke_service": "/action-definitions/{actionDefinitionId}/form/fields/{fieldId}/invoke-service",
    
    # Action Instances
    "action_instances_form_fields": "/action-instances/{actionInstanceId}/form/fields/{fieldId}/document-definitions",
    "action_instances_invoke_service": "/action-instances/{actionInstanceId}/form/fields/{fieldId}/invoke-service",
    
    # App Definitions
    "delete_app_definition": "/app-definitions/{appDefinitionId}",
    
    # App Deployments
    "batch_delete_app_deployments": "/app-deployments/delete",
    "delete_app_deployment": "/app-deployments/{appDeploymentId}",
    
    # Audit Trail
    "audit_trail": "/audit-trail",
    "audit_trail_type_info": "/audit-trail-type-info",
    
    # Case Definitions
    "case_definition_display_json": "/case-definitions/{caseDefinitionId}/display-json",
    "case_definition_start_form": "/case-definitions/{caseDefinitionId}/start-form",
    "case_definition_form_fields": "/case-definitions/{caseDefinitionId}/start-form/fields/{fieldId}/document-definitions",
    "case_definition_invoke_service": "/case-definitions/{caseDefinitionId}/start-form/fields/{fieldId}/invoke-service",
    
    # Case Instances
    "case_instances": "/case-instances",
    "case_instance_detail": "/case-instances/{caseInstanceId}",
    "case_instance_assign": "/case-instances/{caseInstanceId}/assign",
    "case_instance_tasks": "/case-instances/{caseInstanceId}/case-page-tasks",
    "case_instance_comments": "/case-instances/{caseInstanceId}/comments",
    "case_instance_comment": "/case-instances/{caseInstanceId}/comments/{commentId}",
    "case_instance_content": "/case-instances/{caseInstanceId}/content",
    "case_instance_content_data": "/case-instances/{caseInstanceId}/content-service/content-items/{contentItemId}/data",
    "case_instance_content_latest": "/case-instances/{caseInstanceId}/content-service/content-items/{contentItemId}/data/latest",
    "case_instance_content_version": "/case-instances/{caseInstanceId}/content/{contentItemId}/new-version",
    "case_instance_display_json": "/case-instances/{caseInstanceId}/display-json",
    "case_instance_entity_links": "/case-instances/{caseInstanceId}/entity-links",
    "case_instance_form": "/case-instances/{caseInstanceId}/form",
    "case_instance_form_fields": "/case-instances/{caseInstanceId}/form/fields/{fieldId}/document-definitions",
    "case_instance_invoke_service": "/case-instances/{caseInstanceId}/form/fields/{fieldId}/invoke-service",
    "case_instance_identity_links": "/case-instances/{caseInstanceId}/identity-links",
    "case_instance_plan_items": "/case-instances/{caseInstanceId}/plan-item-instances",
    "case_instance_stages": "/case-instances/{caseInstanceId}/stages",
    "case_instance_status_options": "/case-instances/{caseInstanceId}/status-options",
    "case_instance_terminate": "/case-instances/{caseInstanceId}/terminate",
    "case_instance_variables": "/case-instances/{caseInstanceId}/variables",
    "case_instance_variable": "/case-instances/{caseInstanceId}/variables/{variableName}",
    
    # Channels
    "channels": "/channels",
    "channel_detail": "/channels/{channelDefinitionId}",
    
    # Content Management
    "content_folders": "/content-folders",
    "content_folder": "/content-folders/{folderId}",
    "content_folder_items": "/content-folders/{folderId}/items",
    "content_items": "/content-items",
    "content_item": "/content-items/{contentItemId}",
    "content_item_data": "/content-items/{contentItemId}/data",
    "content_item_latest": "/content-items/{contentItemId}/data/latest",
    "content_item_version": "/content-items/{contentItemId}/new-version",
    
    # Dashboard Components
    "dashboard_components": "/dashboard-component-definitions",
    "dashboard_component": "/dashboard-component-definitions/{dashboardComponentDefinitionId}",
    
    # Dashboards
    "dashboards": "/dashboards",
    "dashboard": "/dashboards/{dashboardId}",
    
    # Data Dictionary
    "data_dictionary": "/data-dictionary-definitions",
    "data_dictionary_detail": "/data-dictionary-definitions/{dataDictionaryDefinitionId}",
    
    # Data Tables
    "data_tables": "/data-tables",
    "data_table": "/data-tables/{dataTableDefinitionId}",
    "data_table_favorite_configs": "/datatable-favorite-configs",
    
    # Engine
    "engines": "/engines",
    "engine": "/engines/{engineName}",
    
    # Entity Links
    "entity_links": "/entity-links",
    "entity_link": "/entity-links/{entityLinkId}",
    
    # Execute Query
    "execute_query": "/execute-query",
    
    # Executions
    "executions": "/executions",
    "execution": "/executions/{executionId}",
    "execution_activities": "/executions/{executionId}/activities",
    "execution_variables": "/executions/{executionId}/variables",
    "execution_variable": "/executions/{executionId}/variables/{variableName}",
    
    # Form Custom Components
    "form_custom_components": "/form-custom-components",
    "form_custom_component": "/form-custom-components/{formCustomComponentId}",
    
    # Historic Case Instances
    "historic_case_instances": "/historic-case-instances",
    "historic_case_instance": "/historic-case-instances/{historicCaseInstanceId}",
    "historic_case_instance_comments": "/historic-case-instances/{historicCaseInstanceId}/comments",
    "historic_case_instance_identity_links": "/historic-case-instances/{historicCaseInstanceId}/identity-links",
    "historic_case_instance_stages": "/historic-case-instances/{historicCaseInstanceId}/stages",
    "historic_case_instance_variables": "/historic-case-instances/{historicCaseInstanceId}/variables",
    
    # Historic Process Instances
    "historic_process_instances": "/historic-process-instances",
    "historic_process_instance": "/historic-process-instances/{historicProcessInstanceId}",
    "historic_process_instance_comments": "/historic-process-instances/{historicProcessInstanceId}/comments",
    "historic_process_instance_identity_links": "/historic-process-instances/{historicProcessInstanceId}/identity-links",
    "historic_process_instance_variables": "/historic-process-instances/{historicProcessInstanceId}/variables",
    
    # Housekeeping
    "housekeeping": "/housekeeping",
    
    # Language Configuration
    "language_config": "/language-configuration",
    
    # Pages
    "pages": "/pages",
    "page": "/pages/{pageId}",
    
    # Plan Item Instances
    "plan_item_instances": "/plan-item-instances",
    "plan_item_instance": "/plan-item-instances/{planItemInstanceId}",
    "plan_item_instance_variables": "/plan-item-instances/{planItemInstanceId}/variables",
    "plan_item_instance_variable": "/plan-item-instances/{planItemInstanceId}/variables/{variableName}",
    
    # Platform Definitions
    "platform_definitions": "/platform-definitions",
    "platform_definition": "/platform-definitions/{platformDefinitionId}",
    
    # Platform Deployments
    "platform_deployments": "/platform-deployments",
    "platform_deployment": "/platform-deployments/{platformDeploymentId}",
    
    # Platform Global Search
    "platform_search": "/platform-global-search",
    
    # Platform Management
    "platform_management": "/platform-management",
    
    # Process Definitions
    "process_definitions": "/process-definitions",
    "process_definition": "/process-definitions/{processDefinitionId}",
    "process_definition_display_json": "/process-definitions/{processDefinitionId}/display-json",
    "process_definition_start_form": "/process-definitions/{processDefinitionId}/start-form",
    "process_definition_form_fields": "/process-definitions/{processDefinitionId}/start-form/fields/{fieldId}/document-definitions",
    "process_definition_invoke_service": "/process-definitions/{processDefinitionId}/start-form/fields/{fieldId}/invoke-service",
    
    # Process Instances
    "process_instances": "/process-instances",
    "process_instance": "/process-instances/{processInstanceId}",
    "process_instance_comments": "/process-instances/{processInstanceId}/comments",
    "process_instance_content": "/process-instances/{processInstanceId}/content",
    "process_instance_display_json": "/process-instances/{processInstanceId}/display-json",
    "process_instance_entity_links": "/process-instances/{processInstanceId}/entity-links",
    "process_instance_form": "/process-instances/{processInstanceId}/form",
    "process_instance_identity_links": "/process-instances/{processInstanceId}/identity-links",
    "process_instance_variables": "/process-instances/{processInstanceId}/variables",
    "process_instance_variable": "/process-instances/{processInstanceId}/variables/{variableName}",
    
    # Query Definitions
    "query_definitions": "/query-definitions",
    "query_definition": "/query-definitions/{queryDefinitionId}",
    
    # Reindexing
    "reindex_activities": "/activities/reindex",
    "reindex_activity": "/activities/{activityInstanceId}/reindex",
    "reindex_case_instances": "/case-instances/reindex",
    "reindex_case_instance": "/case-instances/{caseInstanceId}/reindex",
    "reindex_process_instances": "/process-instances/reindex",
    "reindex_process_instance": "/process-instances/{processInstanceId}/reindex",
    "reindex_tasks": "/tasks/reindex",
    "reindex_task": "/tasks/{taskId}/reindex",
    "reindex_work_instances": "/work-instances/reindex",
    "reindex_work_instance": "/work-instances/{workInstanceId}/reindex",
    
    # Reports
    "reports": "/reports",
    "report": "/reports/{reportId}",
    
    # SLA Audit Instances
    "sla_audit_instances": "/sla-audit-instances",
    "sla_audit_instance": "/sla-audit-instances/{slaAuditInstanceId}",
    
    # SLA Definitions
    "sla_definitions": "/sla-definitions",
    "sla_definition": "/sla-definitions/{slaDefinitionId}",
    
    # Secrets
    "secrets": "/secrets",
    "secret": "/secrets/{secretId}",
    
    # Sequence Definitions
    "sequence_definitions": "/sequence-definitions",
    "sequence_definition": "/sequence-definitions/{sequenceDefinitionId}",
    
    # Sequence Values
    "sequence_values": "/sequence-values",
    "sequence_value": "/sequence-values/{sequenceValueId}",
    
    # System Configuration
    "system_config": "/system-configuration",
    "system_config_item": "/system-configuration/{configurationId}",
    
    # Tasks
    "tasks": "/tasks",
    "task": "/tasks/{taskId}",
    "task_assign": "/tasks/{taskId}/assign",
    "task_claim": "/tasks/{taskId}/claim",
    "task_complete": "/tasks/{taskId}/complete",
    "task_comments": "/tasks/{taskId}/comments",
    "task_comment": "/tasks/{taskId}/comments/{commentId}",
    "task_content": "/tasks/{taskId}/content",
    "task_entity_links": "/tasks/{taskId}/entity-links",
    "task_form": "/tasks/{taskId}/form",
    "task_form_fields": "/tasks/{taskId}/form/fields/{fieldId}/document-definitions",
    "task_invoke_service": "/tasks/{taskId}/form/fields/{fieldId}/invoke-service",
    "task_identity_links": "/tasks/{taskId}/identity-links",
    "task_unclaim": "/tasks/{taskId}/unclaim",
    "task_variables": "/tasks/{taskId}/variables",
    "task_variable": "/tasks/{taskId}/variables/{variableName}",
    
    # Template Variations
    "template_variations": "/template-variations",
    "template_variation": "/template-variations/{templateVariationId}",
    
    # Tenant Variables
    "tenant_variables": "/tenant-variables",
    "tenant_variable": "/tenant-variables/{tenantVariableId}",
    
    # Themes
    "themes": "/themes",
    "theme": "/themes/{themeId}",
    
    # User Impersonation
    "user_impersonation": "/user-impersonation",
    
    # User Platform Forms
    "user_platform_forms": "/user-platform-forms",
    "user_platform_form": "/user-platform-forms/{userPlatformFormId}",
    
    # Variable Extractor Definitions
    "variable_extractor_definitions": "/variable-extractor-definitions",
    "variable_extractor_definition": "/variable-extractor-definitions/{variableExtractorDefinitionId}",
    
    # Work Definitions
    "work_definitions": "/work-definitions",
    "work_definition": "/work-definitions/{workDefinitionId}",
    
    # Work Forms
    "work_forms": "/work-forms",
    "work_form": "/work-forms/{workFormId}",
    
    # Work Instances
    "work_instances": "/work-instances",
    "work_instance": "/work-instances/{workInstanceId}",
    "work_instance_assign": "/work-instances/{workInstanceId}/assign",
    "work_instance_comments": "/work-instances/{workInstanceId}/comments",
    "work_instance_content": "/work-instances/{workInstanceId}/content",
    "work_instance_entity_links": "/work-instances/{workInstanceId}/entity-links",
    "work_instance_form": "/work-instances/{workInstanceId}/form",
    "work_instance_identity_links": "/work-instances/{workInstanceId}/identity-links",
    "work_instance_variables": "/work-instances/{workInstanceId}/variables",
    "work_instance_variable": "/work-instances/{workInstanceId}/variables/{variableName}",
}

# Default headers
DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

# File upload headers
MULTIPART_HEADERS = {
    # Content-Type will be set automatically for multipart/form-data
}

# Authentication types
AUTH_TYPES = {
    "BASIC": "basic",
    "TOKEN": "token"
}
