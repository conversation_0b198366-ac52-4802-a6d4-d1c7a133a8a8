"""Database management for Flowable MCP Server"""

import asyncio
import logging
import os
import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from contextlib import asynccontextmanager

from database_models import Base, FlowableAPICall, ProcessInstanceLog, CaseInstanceLog, TaskLog, WorkInstanceLog, AuditLog

logger = logging.getLogger("flowable-mcp-server")

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./flowable_mcp.db")
DATABASE_ENABLED = os.getenv("DATABASE_ENABLED", "true").lower() == "true"


class DatabaseManager:
    """Manages database connections and operations"""
    
    def __init__(self, database_url: str = DATABASE_URL):
        self.database_url = database_url
        self.engine = None
        self.async_session = None
        self.initialized = False
    
    async def initialize(self):
        """Initialize database connection and create tables"""
        if self.initialized:
            return
        
        try:
            # Create async engine
            self.engine = create_async_engine(
                self.database_url,
                echo=False,  # Set to True for SQL debugging
                pool_pre_ping=True,
                pool_recycle=3600
            )
            
            # Create session factory
            self.async_session = sessionmaker(
                self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Create tables
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            
            self.initialized = True
            logger.info("Database initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {str(e)}")
            raise
    
    async def close(self):
        """Close database connections"""
        if self.engine:
            await self.engine.dispose()
            self.initialized = False
            logger.info("Database connections closed")
    
    @asynccontextmanager
    async def get_session(self):
        """Get database session context manager"""
        if not self.initialized:
            await self.initialize()
        
        async with self.async_session() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                logger.error(f"Database session error: {str(e)}")
                raise
            finally:
                await session.close()
    
    async def log_api_call(self, endpoint: str, method: str, request_data: Optional[Dict[str, Any]] = None,
                          response_data: Optional[Dict[str, Any]] = None, status_code: Optional[int] = None,
                          success: bool = False, error_message: Optional[str] = None,
                          user_id: Optional[str] = None, tenant_id: Optional[str] = None) -> Optional[str]:
        """Log an API call to the database"""
        if not DATABASE_ENABLED:
            return None
        
        try:
            async with self.get_session() as session:
                api_call = FlowableAPICall(
                    id=str(uuid.uuid4()),
                    endpoint=endpoint,
                    method=method,
                    request_data=request_data,
                    response_data=response_data,
                    status_code=status_code,
                    success=success,
                    error_message=error_message,
                    user_id=user_id,
                    tenant_id=tenant_id
                )
                session.add(api_call)
                await session.flush()
                return api_call.id
        except Exception as e:
            logger.error(f"Failed to log API call: {str(e)}")
            return None
    
    async def log_process_instance(self, process_data: Dict[str, Any]) -> Optional[str]:
        """Log process instance data"""
        if not DATABASE_ENABLED:
            return None
        
        try:
            async with self.get_session() as session:
                process_log = ProcessInstanceLog(
                    id=str(uuid.uuid4()),
                    process_instance_id=process_data.get("id"),
                    process_definition_id=process_data.get("processDefinitionId"),
                    process_definition_key=process_data.get("processDefinitionKey"),
                    name=process_data.get("name"),
                    business_key=process_data.get("businessKey"),
                    business_status=process_data.get("businessStatus"),
                    start_time=self._parse_datetime(process_data.get("startTime")),
                    end_time=self._parse_datetime(process_data.get("endTime")),
                    start_user_id=process_data.get("startUserId"),
                    tenant_id=process_data.get("tenantId"),
                    variables=process_data.get("variables")
                )
                session.add(process_log)
                await session.flush()
                return process_log.id
        except Exception as e:
            logger.error(f"Failed to log process instance: {str(e)}")
            return None
    
    async def log_case_instance(self, case_data: Dict[str, Any]) -> Optional[str]:
        """Log case instance data"""
        if not DATABASE_ENABLED:
            return None
        
        try:
            async with self.get_session() as session:
                case_log = CaseInstanceLog(
                    id=str(uuid.uuid4()),
                    case_instance_id=case_data.get("id"),
                    case_definition_id=case_data.get("caseDefinitionId"),
                    case_definition_key=case_data.get("caseDefinitionKey"),
                    name=case_data.get("name"),
                    business_key=case_data.get("businessKey"),
                    business_status=case_data.get("businessStatus"),
                    start_time=self._parse_datetime(case_data.get("startTime")),
                    end_time=self._parse_datetime(case_data.get("endTime")),
                    start_user_id=case_data.get("startUserId"),
                    tenant_id=case_data.get("tenantId"),
                    state=case_data.get("state"),
                    assignee=case_data.get("assignee"),
                    owner=case_data.get("owner"),
                    variables=case_data.get("variables")
                )
                session.add(case_log)
                await session.flush()
                return case_log.id
        except Exception as e:
            logger.error(f"Failed to log case instance: {str(e)}")
            return None
    
    async def log_task(self, task_data: Dict[str, Any]) -> Optional[str]:
        """Log task data"""
        if not DATABASE_ENABLED:
            return None
        
        try:
            async with self.get_session() as session:
                task_log = TaskLog(
                    id=str(uuid.uuid4()),
                    task_id=task_data.get("id"),
                    name=task_data.get("name"),
                    description=task_data.get("description"),
                    assignee=task_data.get("assignee"),
                    owner=task_data.get("owner"),
                    create_time=self._parse_datetime(task_data.get("createTime")),
                    due_date=self._parse_datetime(task_data.get("dueDate")),
                    priority=task_data.get("priority"),
                    task_definition_key=task_data.get("taskDefinitionKey"),
                    process_instance_id=task_data.get("processInstanceId"),
                    case_instance_id=task_data.get("caseInstanceId"),
                    scope_id=task_data.get("scopeId"),
                    scope_type=task_data.get("scopeType"),
                    tenant_id=task_data.get("tenantId"),
                    category=task_data.get("category"),
                    form_key=task_data.get("formKey"),
                    state=task_data.get("state"),
                    completed_by=task_data.get("completedBy"),
                    completion_time=self._parse_datetime(task_data.get("endTime")),
                    variables=task_data.get("variables")
                )
                session.add(task_log)
                await session.flush()
                return task_log.id
        except Exception as e:
            logger.error(f"Failed to log task: {str(e)}")
            return None
    
    async def log_work_instance(self, work_data: Dict[str, Any]) -> Optional[str]:
        """Log work instance data"""
        if not DATABASE_ENABLED:
            return None
        
        try:
            async with self.get_session() as session:
                work_log = WorkInstanceLog(
                    id=str(uuid.uuid4()),
                    work_instance_id=work_data.get("id"),
                    name=work_data.get("name"),
                    business_key=work_data.get("businessKey"),
                    business_status=work_data.get("businessStatus"),
                    type=work_data.get("type"),
                    definition_id=work_data.get("definitionId"),
                    definition_key=work_data.get("definitionKey"),
                    definition_name=work_data.get("definitionName"),
                    start_time=self._parse_datetime(work_data.get("startTime")),
                    end_time=self._parse_datetime(work_data.get("endTime")),
                    start_user_id=work_data.get("startUserId"),
                    assignee=work_data.get("assignee"),
                    owner=work_data.get("owner"),
                    tenant_id=work_data.get("tenantId"),
                    variables=work_data.get("variables")
                )
                session.add(work_log)
                await session.flush()
                return work_log.id
        except Exception as e:
            logger.error(f"Failed to log work instance: {str(e)}")
            return None
    
    async def log_audit_entry(self, audit_data: Dict[str, Any]) -> Optional[str]:
        """Log audit entry data"""
        if not DATABASE_ENABLED:
            return None
        
        try:
            async with self.get_session() as session:
                audit_log = AuditLog(
                    id=str(uuid.uuid4()),
                    audit_entry_id=audit_data.get("id"),
                    type=audit_data.get("type"),
                    sub_type=audit_data.get("subType"),
                    scope_id=audit_data.get("scopeId"),
                    scope_type=audit_data.get("scopeType"),
                    scope_definition_id=audit_data.get("scopeDefinitionId"),
                    sub_scope_id=audit_data.get("subScopeId"),
                    external_id=audit_data.get("externalId"),
                    creator_id=audit_data.get("creatorId"),
                    create_time=self._parse_datetime(audit_data.get("createTime")),
                    definition_key=audit_data.get("definitionKey"),
                    payload=audit_data.get("payload")
                )
                session.add(audit_log)
                await session.flush()
                return audit_log.id
        except Exception as e:
            logger.error(f"Failed to log audit entry: {str(e)}")
            return None
    
    def _parse_datetime(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse datetime string to datetime object"""
        if not date_str:
            return None
        
        try:
            # Handle ISO 8601 format
            if 'T' in date_str:
                if date_str.endswith('Z'):
                    return datetime.fromisoformat(date_str[:-1])
                else:
                    return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            else:
                return datetime.fromisoformat(date_str)
        except (ValueError, TypeError):
            logger.warning(f"Failed to parse datetime: {date_str}")
            return None


# Global database manager instance
db_manager = DatabaseManager()
