#!/bin/bash

# Flowable MCP Server Deployment Script
# This script helps deploy the Flowable MCP server in various environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="development"
BUILD_IMAGE=false
PUSH_IMAGE=false
DOCKER_REGISTRY=""
IMAGE_TAG="latest"
COMPOSE_PROFILES=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --environment ENV    Set environment (development|staging|production) [default: development]"
    echo "  -b, --build             Build Docker image"
    echo "  -p, --push              Push Docker image to registry"
    echo "  -r, --registry URL      Docker registry URL"
    echo "  -t, --tag TAG           Docker image tag [default: latest]"
    echo "  --profiles PROFILES     Docker Compose profiles to enable (comma-separated)"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -e development -b                    # Build and deploy for development"
    echo "  $0 -e production -b -p -r my-registry  # Build, push and deploy for production"
    echo "  $0 --profiles postgres,redis           # Deploy with PostgreSQL and Redis"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -b|--build)
            BUILD_IMAGE=true
            shift
            ;;
        -p|--push)
            PUSH_IMAGE=true
            shift
            ;;
        -r|--registry)
            DOCKER_REGISTRY="$2"
            shift 2
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --profiles)
            COMPOSE_PROFILES="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    print_error "Valid environments: development, staging, production"
    exit 1
fi

print_status "Starting deployment for environment: $ENVIRONMENT"

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker daemon is not running"
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose is not installed"
    exit 1
fi

# Set Docker Compose command
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose"
else
    DOCKER_COMPOSE="docker compose"
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p data logs ssl

# Load environment variables
ENV_FILE=".env"
if [[ "$ENVIRONMENT" != "development" ]]; then
    ENV_FILE=".env.$ENVIRONMENT"
fi

if [[ -f "$ENV_FILE" ]]; then
    print_status "Loading environment variables from $ENV_FILE"
    export $(grep -v '^#' "$ENV_FILE" | xargs)
else
    print_warning "Environment file $ENV_FILE not found"
fi

# Build Docker image if requested
if [[ "$BUILD_IMAGE" == true ]]; then
    print_status "Building Docker image..."
    
    IMAGE_NAME="flowable-mcp"
    if [[ -n "$DOCKER_REGISTRY" ]]; then
        IMAGE_NAME="$DOCKER_REGISTRY/flowable-mcp"
    fi
    
    docker build -t "$IMAGE_NAME:$IMAGE_TAG" .
    
    if [[ "$PUSH_IMAGE" == true ]]; then
        if [[ -z "$DOCKER_REGISTRY" ]]; then
            print_error "Docker registry URL is required for pushing images"
            exit 1
        fi
        
        print_status "Pushing Docker image to registry..."
        docker push "$IMAGE_NAME:$IMAGE_TAG"
        print_success "Image pushed successfully"
    fi
    
    print_success "Docker image built successfully"
fi

# Prepare Docker Compose command
COMPOSE_CMD="$DOCKER_COMPOSE"

if [[ -n "$COMPOSE_PROFILES" ]]; then
    IFS=',' read -ra PROFILES <<< "$COMPOSE_PROFILES"
    for profile in "${PROFILES[@]}"; do
        COMPOSE_CMD="$COMPOSE_CMD --profile $profile"
    done
fi

# Use environment-specific compose file if it exists
COMPOSE_FILE="docker-compose.yml"
if [[ -f "docker-compose.$ENVIRONMENT.yml" ]]; then
    COMPOSE_FILE="docker-compose.yml:docker-compose.$ENVIRONMENT.yml"
fi

export COMPOSE_FILE

# Stop existing containers
print_status "Stopping existing containers..."
$COMPOSE_CMD down

# Start services
print_status "Starting services..."
$COMPOSE_CMD up -d

# Wait for services to be healthy
print_status "Waiting for services to be ready..."
sleep 10

# Check service health
if docker ps --filter "name=flowable-mcp" --filter "health=healthy" | grep -q flowable-mcp; then
    print_success "Flowable MCP server is healthy and running"
else
    print_warning "Flowable MCP server health check failed or still starting"
    print_status "Checking logs..."
    $COMPOSE_CMD logs flowable-mcp
fi

# Show running services
print_status "Running services:"
$COMPOSE_CMD ps

# Show useful information
print_success "Deployment completed!"
echo ""
print_status "Service URLs:"
echo "  - Flowable MCP Server: http://localhost:8000"

if [[ "$COMPOSE_PROFILES" == *"postgres"* ]]; then
    echo "  - PostgreSQL: localhost:5432"
fi

if [[ "$COMPOSE_PROFILES" == *"redis"* ]]; then
    echo "  - Redis: localhost:6379"
fi

if [[ "$COMPOSE_PROFILES" == *"nginx"* ]]; then
    echo "  - Nginx: http://localhost (port 80)"
fi

echo ""
print_status "Useful commands:"
echo "  - View logs: $COMPOSE_CMD logs -f"
echo "  - Stop services: $COMPOSE_CMD down"
echo "  - Restart services: $COMPOSE_CMD restart"
echo "  - Update services: $COMPOSE_CMD pull && $COMPOSE_CMD up -d"
