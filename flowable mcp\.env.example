# Flowable Platform Configuration
# Base URL for the Flowable Platform API
FLOWABLE_BASE_URL=http://localhost:8090/platform-api

# Authentication - Use either username/password OR API token
# Basic Authentication
FLOWABLE_USERNAME=admin
FLOWABLE_PASSWORD=test

# OR Bearer Token Authentication (takes precedence if both are set)
# FLOWABLE_API_TOKEN=your_api_token_here

# Database Configuration
# SQLite (default for development)
DATABASE_URL=sqlite+aiosqlite:///./data/flowable_mcp.db

# PostgreSQL (recommended for production)
# DATABASE_URL=postgresql+asyncpg://flowable:password@localhost:5432/flowable_mcp

# MySQL
# DATABASE_URL=mysql+aiomysql://flowable:password@localhost:3306/flowable_mcp

# Enable/disable database logging
DATABASE_ENABLED=true

# Server Configuration
# Port for the MCP server (when running standalone)
SERVER_PORT=8000
SERVER_HOST=0.0.0.0

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s [%(levelname)s] %(name)s: %(message)s

# Performance Configuration
MAX_CONCURRENT_API_CALLS=25
CONNECTION_POOL_SIZE=50
REQUEST_TIMEOUT=60

# Security Configuration
# Enable HTTPS for Flowable API calls
FLOWABLE_VERIFY_SSL=true

# Optional: Custom CA certificate path
# FLOWABLE_CA_CERT_PATH=/path/to/ca-cert.pem

# Optional: Client certificate for mutual TLS
# FLOWABLE_CLIENT_CERT_PATH=/path/to/client-cert.pem
# FLOWABLE_CLIENT_KEY_PATH=/path/to/client-key.pem

# Monitoring Configuration
# Enable health check endpoint
HEALTH_CHECK_ENABLED=true

# Enable metrics collection
METRICS_ENABLED=true

# Optional: External monitoring endpoints
# PROMETHEUS_ENDPOINT=http://localhost:9090
# GRAFANA_ENDPOINT=http://localhost:3000

# Development Configuration
# Enable debug mode (more verbose logging)
DEBUG_MODE=false

# Enable API request/response logging
LOG_API_CALLS=true

# Enable database query logging
LOG_DATABASE_QUERIES=false

# Production Configuration (uncomment for production)
# LOG_LEVEL=WARNING
# DEBUG_MODE=false
# DATABASE_URL=postgresql+asyncpg://flowable:secure_password@db:5432/flowable_mcp
# MAX_CONCURRENT_API_CALLS=100
# CONNECTION_POOL_SIZE=200

# Docker Configuration
# These are used by docker-compose.yml
POSTGRES_PASSWORD=flowable123
REDIS_PASSWORD=redis123

# Nginx Configuration (if using nginx profile)
NGINX_SERVER_NAME=localhost
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# Backup Configuration
# Enable automatic database backups
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=./backups

# Integration Configuration
# Optional: Webhook endpoints for notifications
# WEBHOOK_URL=https://your-webhook-endpoint.com/flowable
# WEBHOOK_SECRET=your_webhook_secret

# Optional: Email notifications
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your_app_password
# SMTP_FROM_EMAIL=<EMAIL>

# Optional: Slack notifications
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Tenant Configuration (for multi-tenant setups)
# DEFAULT_TENANT_ID=tenant1
# TENANT_ISOLATION_ENABLED=false
