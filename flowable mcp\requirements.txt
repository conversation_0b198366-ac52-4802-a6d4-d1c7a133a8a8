# FastAPI server and MCP
fastapi==0.104.1
uvicorn[standard]==0.24.0
httpx[http2]==0.25.2
python-multipart
python-dotenv

# Pydantic & typing
pydantic==2.5.0
pydantic-settings==2.1.0
pydantic_core==2.14.1
annotated-types

# Database
aiosqlite
SQLAlchemy
peewee

# Core utilities
requests==2.31.0

# Optional CLI/dev experience
rich
typer
click

# HTML rendering & docs
markdown-it-py
mdurl
Pygments
colorama

# Flask-based sqlite viewer (optional)
sqlite-web

# Utilities
sniffio
shellingham

# Logging and web utils
h11
blinker
Werkzeug
itsdangerous
Jinja2
MarkupSafe
certifi
idna

# Authentication
cryptography
PyJWT

# Date/time handling
python-dateutil

# JSON handling
orjson

# HTTP Basic Auth
httpx-auth

# MCP Framework
mcp
