"""Database models for Flowable MCP Server"""

from sqlalchemy import Column, String, DateTime, Text, Integer, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any

Base = declarative_base()


class FlowableAPICall(Base):
    """Model to store Flowable API call logs"""
    __tablename__ = "flowable_api_calls"
    
    id = Column(String, primary_key=True)
    endpoint = Column(String, nullable=False)
    method = Column(String, nullable=False)
    request_data = Column(JSON)
    response_data = Column(JSON)
    status_code = Column(Integer)
    success = Column(Boolean, default=False)
    error_message = Column(Text)
    created_at = Column(DateTime, default=func.now())
    user_id = Column(String)
    tenant_id = Column(String)


class ProcessInstanceLog(Base):
    """Model to store process instance information"""
    __tablename__ = "process_instance_logs"
    
    id = Column(String, primary_key=True)
    process_instance_id = Column(String, nullable=False, unique=True)
    process_definition_id = Column(String)
    process_definition_key = Column(String)
    name = Column(String)
    business_key = Column(String)
    business_status = Column(String)
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    start_user_id = Column(String)
    tenant_id = Column(String)
    variables = Column(JSON)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class CaseInstanceLog(Base):
    """Model to store case instance information"""
    __tablename__ = "case_instance_logs"
    
    id = Column(String, primary_key=True)
    case_instance_id = Column(String, nullable=False, unique=True)
    case_definition_id = Column(String)
    case_definition_key = Column(String)
    name = Column(String)
    business_key = Column(String)
    business_status = Column(String)
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    start_user_id = Column(String)
    tenant_id = Column(String)
    state = Column(String)
    assignee = Column(String)
    owner = Column(String)
    variables = Column(JSON)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class TaskLog(Base):
    """Model to store task information"""
    __tablename__ = "task_logs"
    
    id = Column(String, primary_key=True)
    task_id = Column(String, nullable=False, unique=True)
    name = Column(String)
    description = Column(Text)
    assignee = Column(String)
    owner = Column(String)
    create_time = Column(DateTime)
    due_date = Column(DateTime)
    priority = Column(Integer)
    task_definition_key = Column(String)
    process_instance_id = Column(String)
    case_instance_id = Column(String)
    scope_id = Column(String)
    scope_type = Column(String)
    tenant_id = Column(String)
    category = Column(String)
    form_key = Column(String)
    state = Column(String)
    completed_by = Column(String)
    completion_time = Column(DateTime)
    variables = Column(JSON)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class WorkInstanceLog(Base):
    """Model to store work instance information"""
    __tablename__ = "work_instance_logs"
    
    id = Column(String, primary_key=True)
    work_instance_id = Column(String, nullable=False, unique=True)
    name = Column(String)
    business_key = Column(String)
    business_status = Column(String)
    type = Column(String)
    definition_id = Column(String)
    definition_key = Column(String)
    definition_name = Column(String)
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    start_user_id = Column(String)
    assignee = Column(String)
    owner = Column(String)
    tenant_id = Column(String)
    variables = Column(JSON)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class AuditLog(Base):
    """Model to store audit trail information"""
    __tablename__ = "audit_logs"
    
    id = Column(String, primary_key=True)
    audit_entry_id = Column(String, unique=True)
    type = Column(String)
    sub_type = Column(String)
    scope_id = Column(String)
    scope_type = Column(String)
    scope_definition_id = Column(String)
    sub_scope_id = Column(String)
    external_id = Column(String)
    creator_id = Column(String)
    create_time = Column(DateTime)
    definition_key = Column(String)
    payload = Column(JSON)
    created_at = Column(DateTime, default=func.now())


class DeploymentLog(Base):
    """Model to store deployment information"""
    __tablename__ = "deployment_logs"
    
    id = Column(String, primary_key=True)
    deployment_id = Column(String, nullable=False, unique=True)
    name = Column(String)
    deployment_time = Column(DateTime)
    category = Column(String)
    tenant_id = Column(String)
    parent_deployment_id = Column(String)
    resources = Column(JSON)
    created_at = Column(DateTime, default=func.now())


class UserActivity(Base):
    """Model to track user activity"""
    __tablename__ = "user_activities"
    
    id = Column(String, primary_key=True)
    user_id = Column(String, nullable=False)
    activity_type = Column(String, nullable=False)  # task_complete, process_start, etc.
    entity_type = Column(String)  # task, process, case, etc.
    entity_id = Column(String)
    description = Column(Text)
    metadata = Column(JSON)
    tenant_id = Column(String)
    created_at = Column(DateTime, default=func.now())


class SystemConfiguration(Base):
    """Model to store system configuration"""
    __tablename__ = "system_configurations"
    
    id = Column(String, primary_key=True)
    key = Column(String, nullable=False, unique=True)
    value = Column(JSON)
    description = Column(Text)
    category = Column(String)
    tenant_id = Column(String)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    created_by = Column(String)
    updated_by = Column(String)
