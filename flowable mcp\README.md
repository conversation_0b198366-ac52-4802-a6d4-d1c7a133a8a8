# Flowable Open Source MCP Server

A Model Context Protocol (MCP) server that provides comprehensive access to Flowable Open Source workflow and process management capabilities. This server implements various process management, task management, deployment management, and other workflow-related services using the Flowable Open Source REST API.

## 🚨 IMPORTANT: Current Status & Required Fixes

**This MCP server has been converted to Open Source but requires some manual fixes to work properly.**

### Quick Fix Required:
The current code has some duplicate functions and Platform-specific features that need to be removed. See the **Complete Fix Guide** section below for step-by-step instructions.

## Features

- **Process Management**: Start, monitor, and manage process instances
- **Task Management**: Assign, claim, complete, and monitor tasks
- **Deployment Management**: Deploy and manage process definitions
- **Repository Management**: Access process definitions and deployments
- **Variable Management**: Set and retrieve process and task variables
- **Identity Management**: Manage users and groups
- **History Management**: Access historic process and task data
- **Job Management**: Monitor and manage jobs and dead letter jobs
- **Form Management**: Handle task forms and form data
- **Engine Management**: Access engine properties and information
- **High Concurrency**: Optimized for high-throughput operations
- **Database Logging**: Optional database logging for audit and monitoring
- **Multi-API Support**: Supports different Flowable engines (BPMN, DMN, CMMN, Form, Content)

## Installation

### Prerequisites

- Python 3.11 or higher
- Flowable Platform server (running and accessible)
- Docker (optional, for containerized deployment)

### Local Installation

1. Clone or create the project directory:
```bash
mkdir flowable-mcp
cd flowable-mcp
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
```bash
# Copy and edit the environment file
cp .env.example .env
```

4. Configure your `.env` file:
```env
# Flowable Open Source Configuration
FLOWABLE_BASE_URL=http://localhost:8080/flowable-rest/service
FLOWABLE_USERNAME=admin
FLOWABLE_PASSWORD=test

# Individual API URLs (optional)
FLOWABLE_PROCESS_API_BASE_URL=http://localhost:8080/flowable-rest/service
FLOWABLE_DMN_API_BASE_URL=http://localhost:8080/flowable-rest/service
FLOWABLE_CMMN_API_BASE_URL=http://localhost:8080/flowable-rest/service

# Database Configuration (optional)
DATABASE_URL=sqlite+aiosqlite:///./flowable_mcp.db
DATABASE_ENABLED=true
```

5. Run the server:
```bash
python flowable_mcp_server.py
```

### Docker Installation

1. Build and run with Docker Compose:
```bash
# Basic deployment
docker-compose up -d

# With PostgreSQL and Redis
docker-compose --profile postgres --profile redis up -d

# With all services including Nginx
docker-compose --profile postgres --profile redis --profile nginx up -d
```

2. Or use the deployment script:
```bash
# Development deployment
./deploy.sh -e development -b

# Production deployment with registry
./deploy.sh -e production -b -p -r your-registry.com --profiles postgres,redis
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `FLOWABLE_BASE_URL` | Flowable Open Source REST API base URL | `http://localhost:8080/flowable-rest/service` |
| `FLOWABLE_PROCESS_API_BASE_URL` | Process API base URL | Uses `FLOWABLE_BASE_URL` |
| `FLOWABLE_DMN_API_BASE_URL` | DMN API base URL | Uses `FLOWABLE_BASE_URL` |
| `FLOWABLE_CMMN_API_BASE_URL` | CMMN API base URL | Uses `FLOWABLE_BASE_URL` |
| `FLOWABLE_FORM_API_BASE_URL` | Form API base URL | Uses `FLOWABLE_BASE_URL` |
| `FLOWABLE_CONTENT_API_BASE_URL` | Content API base URL | Uses `FLOWABLE_BASE_URL` |
| `FLOWABLE_USERNAME` | Username for basic authentication | `admin` |
| `FLOWABLE_PASSWORD` | Password for basic authentication | `test` |
| `DATABASE_URL` | Database connection URL | `sqlite+aiosqlite:///./flowable_mcp.db` |
| `DATABASE_ENABLED` | Enable database logging | `true` |

### Authentication

The server supports basic authentication for Flowable Open Source:

1. **Basic Authentication**: Use `FLOWABLE_USERNAME` and `FLOWABLE_PASSWORD`

The default credentials for Flowable Open Source are typically `admin`/`test`, but these should be changed in production environments.

## Available Tools

### Engine Info
- `get_engine_info()`: Get Flowable engine information
- `get_engine_properties()`: Get Flowable engine properties

### Process Management
- `create_process_instance(process_definition_key, name?, business_key?, variables?)`: Start a new process instance
- `get_process_instance(process_instance_id)`: Get process instance details
- `get_process_instance_variables(process_instance_id)`: Get process variables
- `set_process_instance_variable(process_instance_id, variable_name, variable_value, variable_type?)`: Set process variable
- `delete_process_instance(process_instance_id, delete_reason?)`: Delete process instance
- `suspend_process_instance(process_instance_id)`: Suspend process instance
- `activate_process_instance(process_instance_id)`: Activate suspended process instance

### Task Management
- `get_tasks(assignee?, candidate_user?, process_instance_id?, name?)`: Get tasks with filters
- `get_task(task_id)`: Get task details
- `update_task(task_id, name?, description?, assignee?, owner?, due_date?, priority?)`: Update task
- `complete_task(task_id, variables?)`: Complete task
- `claim_task(task_id, user_id)`: Claim task for user
- `delegate_task(task_id, user_id)`: Delegate task to user

### Repository Management
- `get_deployments()`: Get all deployments
- `get_deployment(deployment_id)`: Get deployment details
- `get_process_definitions()`: Get all process definitions
- `get_process_definition(process_definition_id)`: Get process definition details

### Identity Management
- `get_users()`: Get all users
- `get_user(user_id)`: Get user details
- `get_groups()`: Get all groups
- `get_group(group_id)`: Get group details

### History Management
- `get_historic_process_instances()`: Get historic process instances
- `get_historic_task_instances()`: Get historic task instances
- `get_historic_activity_instances()`: Get historic activity instances

### Job Management
- `get_jobs()`: Get all jobs
- `get_job(job_id)`: Get job details
- `get_deadletter_jobs()`: Get dead letter jobs

### Utility Tools
- `verify_api_ready()`: Check API connectivity and authentication
- `debug_environment()`: Debug environment configuration

## Usage Examples

### Starting a Process Instance

```python
# Start a simple process
result = await create_process_instance("my-process-key")

# Start with variables
variables = '{"customerName": "John Doe", "amount": 1000}'
result = await create_process_instance(
    "invoice-process", 
    name="Invoice for John Doe",
    business_key="INV-001",
    variables=variables
)
```

### Managing Tasks

```python
# Get tasks for a user
tasks = await get_tasks(assignee="john.doe")

# Claim and complete a task
await claim_task("task-123", "john.doe")
await complete_task("task-123", '{"approved": true}')
```

### Case Management

```python
# Create a case instance
case = await create_case_instance(
    "support-case",
    name="Customer Support Case",
    business_key="CASE-001"
)

# Assign case to user
await assign_case_instance("case-123", "support.agent")

# Add comment
await add_case_instance_comment("case-123", "Customer contacted via phone")
```

## API Documentation

The server provides comprehensive REST API documentation through the Flowable Platform API. All endpoints follow RESTful conventions and return JSON responses.

### Response Format

All API calls return responses in the following format:

```json
{
  "success": true,
  "data": { ... },
  "status_code": 200,
  "message": "Success",
  "error": null
}
```

## Database Schema

When database logging is enabled, the server maintains the following tables:

- `flowable_api_calls`: API call logs
- `process_instance_logs`: Process instance data
- `case_instance_logs`: Case instance data
- `task_logs`: Task data
- `work_instance_logs`: Work instance data
- `audit_logs`: Audit trail data
- `deployment_logs`: Deployment information
- `user_activities`: User activity tracking
- `system_configurations`: System configuration

## Monitoring and Logging

The server provides comprehensive logging and monitoring capabilities:

- **Application Logs**: Detailed logging of all operations
- **Database Logging**: Optional persistence of all workflow data
- **Health Checks**: Built-in health check endpoints
- **Metrics**: Performance and usage metrics
- **Audit Trail**: Complete audit trail of all operations

## Security

- **Authentication**: Supports both basic auth and bearer token authentication
- **HTTPS**: Supports secure connections to Flowable Platform
- **Input Validation**: Comprehensive input validation and sanitization
- **Error Handling**: Secure error handling without information leakage

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

1. Check the documentation
2. Review the logs for error details
3. Use the `debug_environment()` tool for troubleshooting
4. Check Flowable Platform connectivity with `verify_api_ready()`

## Changelog

### Version 1.0.0
- Initial release
- Complete Flowable Open Source API integration
- Process and task management
- Database logging and audit trail
- Docker support and deployment scripts

## 🔧 Complete Fix Guide

**Follow these steps to make the MCP work with Flowable Open Source:**

### Step 1: Fix Duplicate Functions in `flowable_mcp_server.py`

The file currently has duplicate function definitions. **Delete these specific lines:**

```python
# DELETE these exact duplicate functions (keep the first occurrence):

# 1. DELETE lines 584-610: Second create_process_instance() function
# 2. DELETE lines 612-625: Second get_process_instance() function
# 3. DELETE lines 627-640: Second get_process_instance_variables() function
# 4. DELETE lines 642-675: Second set_process_instance_variable() function
# 5. DELETE lines 719-740: Second get_tasks() function
# 6. DELETE lines 742-755: Second get_task() function
# 7. DELETE lines 757-780: Second update_task() function
# 8. DELETE lines 782-805: Second complete_task() function
# 9. DELETE lines 807-820: Second claim_task() function
# 10. DELETE lines 822-835: Second delegate_task() function

# Keep only the FIRST occurrence of each function (around lines 289-580)
```

### Step 2: Remove Platform-Only Features

**DELETE these entire function blocks** (they don't exist in Open Source):

```python
# DELETE these exact functions - Platform/Enterprise only:

# 1. DELETE lines 940-958: get_audit_trail() function - Different in Open Source
# 2. DELETE lines 962-981: get_work_instances() function - Platform only
# 3. DELETE lines 984-997: get_work_instance() function - Platform only
# 4. DELETE lines 1000-1013: assign_work_instance() function - Platform only

# Also DELETE any references to:
# - case_definition_start_form (not available in Open Source)
# - process_instance_comments (not in Open Source REST API)
# - audit_trail endpoints (different structure in Open Source)
```

### Step 3: Fix Task Actions for Open Source

**Replace** the task action methods with Open Source compatible versions:

```python
@mcp.tool()
async def complete_task(task_id: str, variables: Optional[str] = None) -> str:
    """Complete a task - Open Source compatible"""
    await ensure_client_initialized()
    try:
        data = {"action": "complete"}
        if variables:
            try:
                data["variables"] = json.loads(variables)
            except json.JSONDecodeError:
                return "Error: Invalid JSON format for variables"

        logger.info(f"Completing task: {task_id}")
        # Use POST with action for Open Source
        endpoint = f"/runtime/tasks/{task_id}"
        return await make_api_call_with_limits("POST", endpoint, "task", data=data)
    except Exception as e:
        logger.error(f"Error completing task: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def claim_task(task_id: str, user_id: str) -> str:
    """Claim a task - Open Source compatible"""
    await ensure_client_initialized()
    try:
        data = {"action": "claim", "assignee": user_id}
        logger.info(f"Claiming task {task_id} for user {user_id}")
        endpoint = f"/runtime/tasks/{task_id}"
        return await make_api_call_with_limits("POST", endpoint, "task", data=data)
    except Exception as e:
        logger.error(f"Error claiming task: {str(e)}")
        return f"Error: {str(e)}"
```

### Step 4: Update Environment Variables

**Remove token authentication** (Open Source uses Basic Auth only):

```env
# Flowable Open Source Configuration
FLOWABLE_BASE_URL=http://localhost:8080/flowable-rest/service
FLOWABLE_USERNAME=admin
FLOWABLE_PASSWORD=test

# Remove these Platform-specific variables:
# FLOWABLE_API_TOKEN=  # DELETE - Not used in Open Source

# Database (optional)
DATABASE_URL=sqlite+aiosqlite:///./flowable_mcp.db
DATABASE_ENABLED=true
```

### Step 5: Test Your Fixed MCP

```bash
# 1. Start Flowable Open Source
docker run -d -p 8080:8080 --name flowable flowable/all-in-one:latest

# 2. Wait for startup (60 seconds)
sleep 60

# 3. Test API accessibility
curl -u admin:test http://localhost:8080/flowable-rest/service/management/engine

# 4. Run your MCP server
python flowable_mcp_server.py

# 5. Test basic operations:
# - verify_api_ready()
# - get_engine_info()
# - get_process_definitions()
```

### 🚀 Quick Automated Fix (Alternative)

If you prefer an automated approach, create this Python script to fix the issues:

```python
# fix_flowable_mcp.py
import re

def fix_flowable_mcp():
    # Read the server file
    with open('flowable_mcp_server.py', 'r') as f:
        content = f.read()

    # Remove duplicate functions (keep first occurrence)
    patterns_to_remove = [
        r'# Process Instance Management Tools\n@mcp\.tool\(\)\nasync def create_process_instance.*?return f"Error: \{str\(e\)\}"',
        r'@mcp\.tool\(\)\nasync def get_work_instances.*?return f"Error: \{str\(e\)\}"',
        r'@mcp\.tool\(\)\nasync def get_work_instance.*?return f"Error: \{str\(e\)\}"',
        r'@mcp\.tool\(\)\nasync def assign_work_instance.*?return f"Error: \{str\(e\)\}"',
    ]

    # Remove second occurrences and platform-only functions
    lines = content.split('\n')
    fixed_lines = []
    skip_until_next_function = False

    for i, line in enumerate(lines):
        # Skip duplicate and platform-only functions
        if any(pattern in line for pattern in ['async def create_process_instance', 'async def get_work_instances', 'async def get_work_instance', 'async def assign_work_instance']) and i > 600:
            skip_until_next_function = True
            continue

        if skip_until_next_function and (line.startswith('@mcp.tool()') or line.startswith('if __name__')):
            skip_until_next_function = False

        if not skip_until_next_function:
            fixed_lines.append(line)

    # Write fixed content
    with open('flowable_mcp_server.py', 'w') as f:
        f.write('\n'.join(fixed_lines))

    print("✅ Fixed flowable_mcp_server.py")

if __name__ == "__main__":
    fix_flowable_mcp()
```

Run with: `python fix_flowable_mcp.py`

### ✅ After Fixes, Your MCP Will Support:

- ✅ **Process Management**: Create, get, suspend, activate, delete process instances
- ✅ **Task Management**: Get, claim, complete, delegate, update tasks
- ✅ **Repository Management**: Get deployments and process definitions
- ✅ **History Management**: Access historic process and task data
- ✅ **Identity Management**: Manage users and groups
- ✅ **Job Management**: Monitor jobs and dead letter jobs
- ✅ **Engine Management**: Get engine info and properties
- ❌ **NOT**: Work instances, audit trails, case management (Platform-only features)
