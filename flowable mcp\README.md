# Flowable MCP Server

A Model Context Protocol (MCP) server that provides comprehensive access to Flowable Platform workflow and case management capabilities. This server implements various process management, case management, task management, and other workflow-related services using the Flowable Platform REST API.

## Features

- **Process Management**: Start, monitor, and manage process instances
- **Case Management**: Create and manage case instances with full lifecycle support
- **Task Management**: Assign, claim, complete, and monitor tasks
- **Work Instance Management**: Handle work instances and assignments
- **Audit Trail**: Access comprehensive audit logs and monitoring data
- **Form Management**: Handle start forms and task forms
- **Variable Management**: Set and retrieve process, case, and task variables
- **Comment System**: Add and manage comments on instances
- **Definition Management**: Access process and case definitions
- **High Concurrency**: Optimized for high-throughput operations
- **Database Logging**: Optional database logging for audit and monitoring

## Installation

### Prerequisites

- Python 3.11 or higher
- Flowable Platform server (running and accessible)
- Docker (optional, for containerized deployment)

### Local Installation

1. Clone or create the project directory:
```bash
mkdir flowable-mcp
cd flowable-mcp
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
```bash
# Copy and edit the environment file
cp .env.example .env
```

4. Configure your `.env` file:
```env
# Flowable Platform Configuration
FLOWABLE_BASE_URL=http://localhost:8090/platform-api
FLOWABLE_USERNAME=your_username
FLOWABLE_PASSWORD=your_password
# OR use API token instead of username/password
FLOWABLE_API_TOKEN=your_api_token

# Database Configuration (optional)
DATABASE_URL=sqlite+aiosqlite:///./flowable_mcp.db
DATABASE_ENABLED=true
```

5. Run the server:
```bash
python flowable_mcp_server.py
```

### Docker Installation

1. Build and run with Docker Compose:
```bash
# Basic deployment
docker-compose up -d

# With PostgreSQL and Redis
docker-compose --profile postgres --profile redis up -d

# With all services including Nginx
docker-compose --profile postgres --profile redis --profile nginx up -d
```

2. Or use the deployment script:
```bash
# Development deployment
./deploy.sh -e development -b

# Production deployment with registry
./deploy.sh -e production -b -p -r your-registry.com --profiles postgres,redis
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `FLOWABLE_BASE_URL` | Flowable Platform API base URL | `http://localhost:8090/platform-api` |
| `FLOWABLE_USERNAME` | Username for basic authentication | - |
| `FLOWABLE_PASSWORD` | Password for basic authentication | - |
| `FLOWABLE_API_TOKEN` | API token for bearer authentication | - |
| `DATABASE_URL` | Database connection URL | `sqlite+aiosqlite:///./flowable_mcp.db` |
| `DATABASE_ENABLED` | Enable database logging | `true` |

### Authentication

The server supports two authentication methods:

1. **Basic Authentication**: Use `FLOWABLE_USERNAME` and `FLOWABLE_PASSWORD`
2. **Bearer Token**: Use `FLOWABLE_API_TOKEN`

If both are provided, bearer token authentication takes precedence.

## Available Tools

### Application Info
- `get_application_info()`: Get Flowable application information and version details

### Process Management
- `create_process_instance(process_definition_key, name?, business_key?, variables?)`: Start a new process instance
- `get_process_instance(process_instance_id)`: Get process instance details
- `get_process_instance_variables(process_instance_id)`: Get process variables
- `set_process_instance_variable(process_instance_id, variable_name, variable_value, variable_type?)`: Set process variable
- `get_process_instance_comments(process_instance_id)`: Get process comments
- `add_process_instance_comment(process_instance_id, content)`: Add process comment

### Case Management
- `create_case_instance(case_definition_key, name?, business_key?, variables?)`: Start a new case instance
- `get_case_instance(case_instance_id)`: Get case instance details
- `get_case_instance_tasks(case_instance_id)`: Get case tasks
- `assign_case_instance(case_instance_id, assignee)`: Assign case to user
- `get_case_instance_comments(case_instance_id)`: Get case comments
- `add_case_instance_comment(case_instance_id, content)`: Add case comment
- `get_case_instance_variables(case_instance_id)`: Get case variables
- `set_case_instance_variable(case_instance_id, variable_name, variable_value, variable_type?)`: Set case variable

### Task Management
- `get_tasks(assignee?, candidate_user?, process_instance_id?, case_instance_id?)`: Get tasks with filters
- `get_task(task_id)`: Get task details
- `assign_task(task_id, assignee)`: Assign task to user
- `claim_task(task_id, user_id)`: Claim task for user
- `complete_task(task_id, variables?)`: Complete task
- `get_task_variables(task_id)`: Get task variables
- `set_task_variable(task_id, variable_name, variable_value, variable_type?)`: Set task variable

### Work Instance Management
- `get_work_instances(assignee?, type_filter?)`: Get work instances
- `get_work_instance(work_instance_id)`: Get work instance details
- `assign_work_instance(work_instance_id, assignee)`: Assign work instance

### Definition Management
- `get_process_definitions()`: Get all process definitions
- `get_process_definition(process_definition_id)`: Get process definition details
- `get_case_definition_start_form(case_definition_id)`: Get case start form
- `get_process_definition_start_form(process_definition_id)`: Get process start form

### Audit and Monitoring
- `get_audit_trail(scope_id?, scope_type?, type_filter?)`: Get audit trail data

### Utility Tools
- `verify_api_ready()`: Check API connectivity and authentication
- `debug_environment()`: Debug environment configuration

## Usage Examples

### Starting a Process Instance

```python
# Start a simple process
result = await create_process_instance("my-process-key")

# Start with variables
variables = '{"customerName": "John Doe", "amount": 1000}'
result = await create_process_instance(
    "invoice-process", 
    name="Invoice for John Doe",
    business_key="INV-001",
    variables=variables
)
```

### Managing Tasks

```python
# Get tasks for a user
tasks = await get_tasks(assignee="john.doe")

# Claim and complete a task
await claim_task("task-123", "john.doe")
await complete_task("task-123", '{"approved": true}')
```

### Case Management

```python
# Create a case instance
case = await create_case_instance(
    "support-case",
    name="Customer Support Case",
    business_key="CASE-001"
)

# Assign case to user
await assign_case_instance("case-123", "support.agent")

# Add comment
await add_case_instance_comment("case-123", "Customer contacted via phone")
```

## API Documentation

The server provides comprehensive REST API documentation through the Flowable Platform API. All endpoints follow RESTful conventions and return JSON responses.

### Response Format

All API calls return responses in the following format:

```json
{
  "success": true,
  "data": { ... },
  "status_code": 200,
  "message": "Success",
  "error": null
}
```

## Database Schema

When database logging is enabled, the server maintains the following tables:

- `flowable_api_calls`: API call logs
- `process_instance_logs`: Process instance data
- `case_instance_logs`: Case instance data
- `task_logs`: Task data
- `work_instance_logs`: Work instance data
- `audit_logs`: Audit trail data
- `deployment_logs`: Deployment information
- `user_activities`: User activity tracking
- `system_configurations`: System configuration

## Monitoring and Logging

The server provides comprehensive logging and monitoring capabilities:

- **Application Logs**: Detailed logging of all operations
- **Database Logging**: Optional persistence of all workflow data
- **Health Checks**: Built-in health check endpoints
- **Metrics**: Performance and usage metrics
- **Audit Trail**: Complete audit trail of all operations

## Security

- **Authentication**: Supports both basic auth and bearer token authentication
- **HTTPS**: Supports secure connections to Flowable Platform
- **Input Validation**: Comprehensive input validation and sanitization
- **Error Handling**: Secure error handling without information leakage

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

1. Check the documentation
2. Review the logs for error details
3. Use the `debug_environment()` tool for troubleshooting
4. Check Flowable Platform connectivity with `verify_api_ready()`

## Changelog

### Version 1.0.0
- Initial release
- Complete Flowable Platform API integration
- Process, case, and task management
- Database logging and audit trail
- Docker support and deployment scripts
