# Flowable Open Source MCP Server

A Model Context Protocol (MCP) server that provides comprehensive access to Flowable Open Source workflow and process management capabilities. This server implements various process management, task management, deployment management, and other workflow-related services using the Flowable Open Source REST API.

## Features

- **Process Management**: Start, monitor, and manage process instances
- **Task Management**: Assign, claim, complete, and monitor tasks
- **Deployment Management**: Deploy and manage process definitions
- **Repository Management**: Access process definitions and deployments
- **Variable Management**: Set and retrieve process and task variables
- **Identity Management**: Manage users and groups
- **History Management**: Access historic process and task data
- **Job Management**: Monitor and manage jobs and dead letter jobs
- **Form Management**: Handle task forms and form data
- **Engine Management**: Access engine properties and information
- **High Concurrency**: Optimized for high-throughput operations
- **Database Logging**: Optional database logging for audit and monitoring
- **Multi-API Support**: Supports different Flowable engines (BPMN, DMN, CMMN, Form, Content)

## Installation

### Prerequisites

- Python 3.11 or higher
- Flowable Platform server (running and accessible)
- Docker (optional, for containerized deployment)

### Local Installation

1. Clone or create the project directory:
```bash
mkdir flowable-mcp
cd flowable-mcp
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
```bash
# Copy and edit the environment file
cp .env.example .env
```

4. Configure your `.env` file:
```env
# Flowable Open Source Configuration
FLOWABLE_BASE_URL=http://localhost:8080/flowable-rest/service
FLOWABLE_USERNAME=admin
FLOWABLE_PASSWORD=test

# Individual API URLs (optional)
FLOWABLE_PROCESS_API_BASE_URL=http://localhost:8080/flowable-rest/service
FLOWABLE_DMN_API_BASE_URL=http://localhost:8080/flowable-rest/service
FLOWABLE_CMMN_API_BASE_URL=http://localhost:8080/flowable-rest/service

# Database Configuration (optional)
DATABASE_URL=sqlite+aiosqlite:///./flowable_mcp.db
DATABASE_ENABLED=true
```

5. Run the server:
```bash
python flowable_mcp_server.py
```

### Docker Installation

1. Build and run with Docker Compose:
```bash
# Basic deployment
docker-compose up -d

# With PostgreSQL and Redis
docker-compose --profile postgres --profile redis up -d

# With all services including Nginx
docker-compose --profile postgres --profile redis --profile nginx up -d
```

2. Or use the deployment script:
```bash
# Development deployment
./deploy.sh -e development -b

# Production deployment with registry
./deploy.sh -e production -b -p -r your-registry.com --profiles postgres,redis
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `FLOWABLE_BASE_URL` | Flowable Open Source REST API base URL | `http://localhost:8080/flowable-rest/service` |
| `FLOWABLE_PROCESS_API_BASE_URL` | Process API base URL | Uses `FLOWABLE_BASE_URL` |
| `FLOWABLE_DMN_API_BASE_URL` | DMN API base URL | Uses `FLOWABLE_BASE_URL` |
| `FLOWABLE_CMMN_API_BASE_URL` | CMMN API base URL | Uses `FLOWABLE_BASE_URL` |
| `FLOWABLE_FORM_API_BASE_URL` | Form API base URL | Uses `FLOWABLE_BASE_URL` |
| `FLOWABLE_CONTENT_API_BASE_URL` | Content API base URL | Uses `FLOWABLE_BASE_URL` |
| `FLOWABLE_USERNAME` | Username for basic authentication | `admin` |
| `FLOWABLE_PASSWORD` | Password for basic authentication | `test` |
| `DATABASE_URL` | Database connection URL | `sqlite+aiosqlite:///./flowable_mcp.db` |
| `DATABASE_ENABLED` | Enable database logging | `true` |

### Authentication

The server supports basic authentication for Flowable Open Source:

1. **Basic Authentication**: Use `FLOWABLE_USERNAME` and `FLOWABLE_PASSWORD`

The default credentials for Flowable Open Source are typically `admin`/`test`, but these should be changed in production environments.

## Available Tools

### Engine Info
- `get_engine_info()`: Get Flowable engine information
- `get_engine_properties()`: Get Flowable engine properties

### Process Management
- `create_process_instance(process_definition_key, name?, business_key?, variables?)`: Start a new process instance
- `get_process_instance(process_instance_id)`: Get process instance details
- `get_process_instance_variables(process_instance_id)`: Get process variables
- `set_process_instance_variable(process_instance_id, variable_name, variable_value, variable_type?)`: Set process variable
- `delete_process_instance(process_instance_id, delete_reason?)`: Delete process instance
- `suspend_process_instance(process_instance_id)`: Suspend process instance
- `activate_process_instance(process_instance_id)`: Activate suspended process instance

### Task Management
- `get_tasks(assignee?, candidate_user?, process_instance_id?, name?)`: Get tasks with filters
- `get_task(task_id)`: Get task details
- `update_task(task_id, name?, description?, assignee?, owner?, due_date?, priority?)`: Update task
- `complete_task(task_id, variables?)`: Complete task
- `claim_task(task_id, user_id)`: Claim task for user
- `delegate_task(task_id, user_id)`: Delegate task to user

### Repository Management
- `get_deployments()`: Get all deployments
- `get_deployment(deployment_id)`: Get deployment details
- `get_process_definitions()`: Get all process definitions
- `get_process_definition(process_definition_id)`: Get process definition details

### Identity Management
- `get_users()`: Get all users
- `get_user(user_id)`: Get user details
- `get_groups()`: Get all groups
- `get_group(group_id)`: Get group details

### History Management
- `get_historic_process_instances()`: Get historic process instances
- `get_historic_task_instances()`: Get historic task instances
- `get_historic_activity_instances()`: Get historic activity instances

### Job Management
- `get_jobs()`: Get all jobs
- `get_job(job_id)`: Get job details
- `get_deadletter_jobs()`: Get dead letter jobs

### Utility Tools
- `verify_api_ready()`: Check API connectivity and authentication
- `debug_environment()`: Debug environment configuration

## Usage Examples

### Starting a Process Instance

```python
# Start a simple process
result = await create_process_instance("my-process-key")

# Start with variables
variables = '{"customerName": "John Doe", "amount": 1000}'
result = await create_process_instance(
    "invoice-process", 
    name="Invoice for John Doe",
    business_key="INV-001",
    variables=variables
)
```

### Managing Tasks

```python
# Get tasks for a user
tasks = await get_tasks(assignee="john.doe")

# Claim and complete a task
await claim_task("task-123", "john.doe")
await complete_task("task-123", '{"approved": true}')
```

### Case Management

```python
# Create a case instance
case = await create_case_instance(
    "support-case",
    name="Customer Support Case",
    business_key="CASE-001"
)

# Assign case to user
await assign_case_instance("case-123", "support.agent")

# Add comment
await add_case_instance_comment("case-123", "Customer contacted via phone")
```

## API Documentation

The server provides comprehensive REST API documentation through the Flowable Platform API. All endpoints follow RESTful conventions and return JSON responses.

### Response Format

All API calls return responses in the following format:

```json
{
  "success": true,
  "data": { ... },
  "status_code": 200,
  "message": "Success",
  "error": null
}
```

## Database Schema

When database logging is enabled, the server maintains the following tables:

- `flowable_api_calls`: API call logs
- `process_instance_logs`: Process instance data
- `case_instance_logs`: Case instance data
- `task_logs`: Task data
- `work_instance_logs`: Work instance data
- `audit_logs`: Audit trail data
- `deployment_logs`: Deployment information
- `user_activities`: User activity tracking
- `system_configurations`: System configuration

## Monitoring and Logging

The server provides comprehensive logging and monitoring capabilities:

- **Application Logs**: Detailed logging of all operations
- **Database Logging**: Optional persistence of all workflow data
- **Health Checks**: Built-in health check endpoints
- **Metrics**: Performance and usage metrics
- **Audit Trail**: Complete audit trail of all operations

## Security

- **Authentication**: Supports both basic auth and bearer token authentication
- **HTTPS**: Supports secure connections to Flowable Platform
- **Input Validation**: Comprehensive input validation and sanitization
- **Error Handling**: Secure error handling without information leakage

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

1. Check the documentation
2. Review the logs for error details
3. Use the `debug_environment()` tool for troubleshooting
4. Check Flowable Platform connectivity with `verify_api_ready()`

## Changelog

### Version 1.0.0
- Initial release
- Complete Flowable Platform API integration
- Process, case, and task management
- Database logging and audit trail
- Docker support and deployment scripts
