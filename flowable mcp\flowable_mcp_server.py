#!/usr/bin/env python3
"""
Flowable Platform MCP Server

A Model Context Protocol server that provides Flowable Platform workflow and case management tools
using the Flowable Platform REST API. This server implements various process management, case management,
task management, and other workflow-related services.
"""

import json
import logging
import sys
import signal
import os
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional, List

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("Environment variables loaded from .env file", file=sys.stderr)
    # Debug: Check if credentials are loaded
    username = os.getenv("FLOWABLE_USERNAME")
    password = os.getenv("FLOWABLE_PASSWORD")
    api_token = os.getenv("FLOWABLE_API_TOKEN")
    if username and password:
        print(f"Basic auth credentials loaded successfully (username: {username})", file=sys.stderr)
    elif api_token:
        print(f"API token loaded successfully (length: {len(api_token)})", file=sys.stderr)
    else:
        print("WARNING: No authentication credentials found in environment", file=sys.stderr)
except ImportError:
    print("python-dotenv not installed, skipping .env file loading", file=sys.stderr)
except Exception as e:
    print(f"Could not load .env file: {e}", file=sys.stderr)

from mcp.server.fastmcp import FastMCP

from flowable_client import FlowableClient
from config import ENDPOINTS

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s [%(levelname)s] %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger("flowable-mcp-server")

MAX_CONCURRENT_API_CALLS = 25  # Limit concurrent API calls

# Semaphores for rate limiting
api_semaphore = asyncio.Semaphore(MAX_CONCURRENT_API_CALLS)

# Client manager for Flowable clients
class ClientManager:
    def __init__(self):
        self.active_clients = set()
    
    @asynccontextmanager
    async def get_client(self):
        client = None
        try:
            client = FlowableClient()
            await client.__aenter__()
            self.active_clients.add(client)
            yield client
        finally:
            if client:
                self.active_clients.discard(client)
                await client.__aexit__(None, None, None)
                await client.close()

client_manager = ClientManager()

# Create the FastMCP server
mcp = FastMCP("flowable-platform-server")

# Initialize client on server startup
async def ensure_client_initialized():
    try:
        logger.info("Flowable MCP Server initialized")
    except Exception as e:
        logger.error(f"Failed to initialize services: {str(e)}")
        raise e

async def make_api_call_with_limits(method: str, endpoint: str, data: Optional[Dict[str, Any]] = None, 
                                   params: Optional[Dict[str, Any]] = None, **path_params) -> str:
    """Make API call with concurrency control
    
    Args:
        method: HTTP method (GET, POST, PUT, DELETE)
        endpoint: API endpoint to call
        data: Request data for POST/PUT requests
        params: Query parameters
        **path_params: Path parameters for URL formatting
    """
    async with api_semaphore:  # Limit concurrent API requests
        async with client_manager.get_client() as client:
            try:
                if method.upper() == "GET":
                    response = await client.get(endpoint, params=params, **path_params)
                elif method.upper() == "POST":
                    response = await client.post_json(endpoint, data=data, **path_params)
                elif method.upper() == "PUT":
                    response = await client.put(endpoint, data=data, **path_params)
                elif method.upper() == "DELETE":
                    response = await client.delete(endpoint, params=params, **path_params)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                # Return formatted response
                response_json = {
                    'success': response.success,
                    'data': response.data,
                    'status_code': response.status_code,
                    'message': response.message or 'Success',
                    'error': response.error
                }
                
                return json.dumps(response_json, indent=2)
                
            except Exception as e:
                logger.error(f"API call failed: {str(e)}")
                return json.dumps({
                    'success': False,
                    'error': f"Request failed: {str(e)}",
                    'status_code': None
                })

@mcp.tool()
async def verify_api_ready() -> str:
    """Check if the Flowable API client is initialized and ready"""
    await ensure_client_initialized()
    try:
        from config import FLOWABLE_USERNAME, FLOWABLE_PASSWORD, FLOWABLE_API_TOKEN, BASE_URL
        
        if not (FLOWABLE_USERNAME and FLOWABLE_PASSWORD) and not FLOWABLE_API_TOKEN:
            return "Error: No authentication credentials found. Please set FLOWABLE_USERNAME/FLOWABLE_PASSWORD or FLOWABLE_API_TOKEN environment variables."

        logger.info(f"Testing API connectivity to {BASE_URL}")

        # Try a simple API call to verify connectivity and authentication
        async with client_manager.get_client() as client:
            response = await client.get(ENDPOINTS["about_info"])

        if response.status_code == 401:
            return "Error: Invalid credentials. Please check your FLOWABLE_USERNAME/FLOWABLE_PASSWORD or FLOWABLE_API_TOKEN."
        elif response.status_code == 403:
            return "Error: Authentication successful but access forbidden. Check user permissions."
        elif response.status_code is None:
            # Network error occurred
            return f"Network Error: {response.error}\n\nTroubleshooting steps:\n1. Check if you can access {BASE_URL} in your browser\n2. Verify firewall allows HTTPS connections\n3. If behind corporate network, check proxy settings"

        return f"API client ready and authenticated (Status: {response.status_code})"
    except Exception as e:
        logger.error(f"API readiness check failed: {str(e)}")
        return f"Error verifying API readiness: {str(e)}\n\nThis appears to be a network connectivity issue. Please check your internet connection and firewall settings."

@mcp.tool()
async def debug_environment() -> str:
    """Debug environment variables and configuration"""
    import os
    import socket
    from config import FLOWABLE_USERNAME, FLOWABLE_PASSWORD, FLOWABLE_API_TOKEN, BASE_URL

    # Test network connectivity
    connectivity_test = {}
    try:
        # Extract hostname from BASE_URL
        from urllib.parse import urlparse
        parsed_url = urlparse(BASE_URL)
        hostname = parsed_url.hostname
        port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
        
        # Test DNS resolution
        result = socket.getaddrinfo(hostname, port, socket.AF_INET)
        ip = result[0][4][0]
        connectivity_test["dns_resolution"] = f"✓ {hostname} -> {ip}"

        # Test port connectivity
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        port_result = sock.connect_ex((ip, port))
        sock.close()

        if port_result == 0:
            connectivity_test[f"port_{port}"] = f"✓ Port {port} is open"
        else:
            connectivity_test[f"port_{port}"] = f"✗ Port {port} blocked (error {port_result})"

    except Exception as e:
        connectivity_test["error"] = f"Network test failed: {str(e)}"

    debug_info = {
        "environment_check": {
            "username_set": bool(FLOWABLE_USERNAME),
            "password_set": bool(FLOWABLE_PASSWORD),
            "api_token_set": bool(FLOWABLE_API_TOKEN),
            "api_token_length": len(FLOWABLE_API_TOKEN) if FLOWABLE_API_TOKEN else 0,
            "base_url": BASE_URL
        },
        "connectivity_test": connectivity_test,
        "os_environment": {
            "flowable_vars": {k: v[:20] + "..." if len(v) > 20 else v
                            for k, v in os.environ.items()
                            if 'FLOWABLE' in k.upper()}
        },
        "client_status": {
            "client_manager_initialized": hasattr(client_manager, "active_clients"),
            "active_clients": len(client_manager.active_clients) if hasattr(client_manager, "active_clients") else 0
        }
    }

    return json.dumps(debug_info, indent=2)

mcp.startup_handler = ensure_client_initialized

# Clean up client on exit
def cleanup_handler(signum, _frame):
    """Signal handler for cleanup"""
    logger.info("Received signal %s, cleaning up...", signum)
    import asyncio

    def run_cleanup():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        async def cleanup():
            # Close all active Flowable clients
            if hasattr(client_manager, "active_clients") and client_manager.active_clients:
                for client in list(client_manager.active_clients):
                    try:
                        await client.__aexit__(None, None, None)
                        await client.close()
                    except Exception as e:
                        logger.error("Error closing Flowable client: %s", str(e))
                client_manager.active_clients.clear()
                logger.info("All Flowable clients closed successfully")

        try:
            loop.run_until_complete(cleanup())
        except Exception as e:
            logger.error("Error during cleanup: %s", str(e))
        finally:
            loop.close()

    try:
        run_cleanup()
    except Exception as e:
        logger.error("Error during cleanup: %s", str(e))

    sys.exit(0)

# Register cleanup handler for signals
signal.signal(signal.SIGINT, cleanup_handler)
signal.signal(signal.SIGTERM, cleanup_handler)

logger.info("Flowable MCP Server initialized and ready")

# Application Info Tools
@mcp.tool()
async def get_application_info() -> str:
    """Get Flowable application information and version details"""
    await ensure_client_initialized()
    try:
        logger.info("Getting application info")
        return await make_api_call_with_limits("GET", ENDPOINTS["about_info"])
    except Exception as e:
        logger.error(f"Error getting application info: {str(e)}")
        return f"Error: {str(e)}"

# Case Instance Management Tools
@mcp.tool()
async def create_case_instance(case_definition_key: str, name: Optional[str] = None,
                              business_key: Optional[str] = None, variables: Optional[str] = None) -> str:
    """Start a new case instance

    Args:
        case_definition_key: The key of the case definition to start
        name: Optional name for the case instance
        business_key: Optional business key for the case instance
        variables: Optional JSON string of variables to set on the case instance
    """
    await ensure_client_initialized()
    try:
        data = {"caseDefinitionKey": case_definition_key}

        if name:
            data["name"] = name
        if business_key:
            data["businessKey"] = business_key
        if variables:
            try:
                data["variables"] = json.loads(variables)
            except json.JSONDecodeError:
                return "Error: Invalid JSON format for variables"

        logger.info(f"Creating case instance for definition key: {case_definition_key}")
        return await make_api_call_with_limits("POST", ENDPOINTS["case_instances"], data=data)
    except Exception as e:
        logger.error(f"Error creating case instance: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_case_instance(case_instance_id: str) -> str:
    """Get details of a specific case instance

    Args:
        case_instance_id: The ID of the case instance to retrieve
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting case instance: {case_instance_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["case_instance_detail"],
                                             caseInstanceId=case_instance_id)
    except Exception as e:
        logger.error(f"Error getting case instance: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_case_instance_tasks(case_instance_id: str) -> str:
    """Get tasks for a specific case instance

    Args:
        case_instance_id: The ID of the case instance
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting tasks for case instance: {case_instance_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["case_instance_tasks"],
                                             caseInstanceId=case_instance_id)
    except Exception as e:
        logger.error(f"Error getting case instance tasks: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def assign_case_instance(case_instance_id: str, assignee: str) -> str:
    """Assign a case instance to a user

    Args:
        case_instance_id: The ID of the case instance
        assignee: The user ID to assign the case to
    """
    await ensure_client_initialized()
    try:
        data = {"assignee": assignee}
        logger.info(f"Assigning case instance {case_instance_id} to {assignee}")
        return await make_api_call_with_limits("POST", ENDPOINTS["case_instance_assign"],
                                             data=data, caseInstanceId=case_instance_id)
    except Exception as e:
        logger.error(f"Error assigning case instance: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_case_instance_comments(case_instance_id: str) -> str:
    """Get comments for a case instance

    Args:
        case_instance_id: The ID of the case instance
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting comments for case instance: {case_instance_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["case_instance_comments"],
                                             caseInstanceId=case_instance_id)
    except Exception as e:
        logger.error(f"Error getting case instance comments: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def add_case_instance_comment(case_instance_id: str, content: str) -> str:
    """Add a comment to a case instance

    Args:
        case_instance_id: The ID of the case instance
        content: The comment content
    """
    await ensure_client_initialized()
    try:
        data = {"content": content}
        logger.info(f"Adding comment to case instance: {case_instance_id}")
        return await make_api_call_with_limits("POST", ENDPOINTS["case_instance_comments"],
                                             data=data, caseInstanceId=case_instance_id)
    except Exception as e:
        logger.error(f"Error adding case instance comment: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_case_instance_variables(case_instance_id: str) -> str:
    """Get variables for a case instance

    Args:
        case_instance_id: The ID of the case instance
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting variables for case instance: {case_instance_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["case_instance_variables"],
                                             caseInstanceId=case_instance_id)
    except Exception as e:
        logger.error(f"Error getting case instance variables: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def set_case_instance_variable(case_instance_id: str, variable_name: str,
                                   variable_value: str, variable_type: str = "string") -> str:
    """Set a variable on a case instance

    Args:
        case_instance_id: The ID of the case instance
        variable_name: The name of the variable
        variable_value: The value of the variable
        variable_type: The type of the variable (string, integer, boolean, etc.)
    """
    await ensure_client_initialized()
    try:
        # Convert value based on type
        if variable_type == "integer":
            value = int(variable_value)
        elif variable_type == "boolean":
            value = variable_value.lower() in ("true", "1", "yes")
        elif variable_type == "json":
            value = json.loads(variable_value)
        else:
            value = variable_value

        data = {
            "name": variable_name,
            "type": variable_type,
            "value": value
        }

        logger.info(f"Setting variable {variable_name} on case instance: {case_instance_id}")
        return await make_api_call_with_limits("PUT", ENDPOINTS["case_instance_variable"],
                                             data=data, caseInstanceId=case_instance_id,
                                             variableName=variable_name)
    except Exception as e:
        logger.error(f"Error setting case instance variable: {str(e)}")
        return f"Error: {str(e)}"

# Process Instance Management Tools
@mcp.tool()
async def create_process_instance(process_definition_key: str, name: Optional[str] = None,
                                business_key: Optional[str] = None, variables: Optional[str] = None) -> str:
    """Start a new process instance

    Args:
        process_definition_key: The key of the process definition to start
        name: Optional name for the process instance
        business_key: Optional business key for the process instance
        variables: Optional JSON string of variables to set on the process instance
    """
    await ensure_client_initialized()
    try:
        data = {"processDefinitionKey": process_definition_key}

        if name:
            data["name"] = name
        if business_key:
            data["businessKey"] = business_key
        if variables:
            try:
                data["variables"] = json.loads(variables)
            except json.JSONDecodeError:
                return "Error: Invalid JSON format for variables"

        logger.info(f"Creating process instance for definition key: {process_definition_key}")
        return await make_api_call_with_limits("POST", ENDPOINTS["process_instances"], data=data)
    except Exception as e:
        logger.error(f"Error creating process instance: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_process_instance(process_instance_id: str) -> str:
    """Get details of a specific process instance

    Args:
        process_instance_id: The ID of the process instance to retrieve
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting process instance: {process_instance_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["process_instance"],
                                             processInstanceId=process_instance_id)
    except Exception as e:
        logger.error(f"Error getting process instance: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_process_instance_variables(process_instance_id: str) -> str:
    """Get variables for a process instance

    Args:
        process_instance_id: The ID of the process instance
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting variables for process instance: {process_instance_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["process_instance_variables"],
                                             processInstanceId=process_instance_id)
    except Exception as e:
        logger.error(f"Error getting process instance variables: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def set_process_instance_variable(process_instance_id: str, variable_name: str,
                                      variable_value: str, variable_type: str = "string") -> str:
    """Set a variable on a process instance

    Args:
        process_instance_id: The ID of the process instance
        variable_name: The name of the variable
        variable_value: The value of the variable
        variable_type: The type of the variable (string, integer, boolean, etc.)
    """
    await ensure_client_initialized()
    try:
        # Convert value based on type
        if variable_type == "integer":
            value = int(variable_value)
        elif variable_type == "boolean":
            value = variable_value.lower() in ("true", "1", "yes")
        elif variable_type == "json":
            value = json.loads(variable_value)
        else:
            value = variable_value

        data = {
            "name": variable_name,
            "type": variable_type,
            "value": value
        }

        logger.info(f"Setting variable {variable_name} on process instance: {process_instance_id}")
        return await make_api_call_with_limits("PUT", ENDPOINTS["process_instance_variable"],
                                             data=data, processInstanceId=process_instance_id,
                                             variableName=variable_name)
    except Exception as e:
        logger.error(f"Error setting process instance variable: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_process_instance_comments(process_instance_id: str) -> str:
    """Get comments for a process instance

    Args:
        process_instance_id: The ID of the process instance
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting comments for process instance: {process_instance_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["process_instance_comments"],
                                             processInstanceId=process_instance_id)
    except Exception as e:
        logger.error(f"Error getting process instance comments: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def add_process_instance_comment(process_instance_id: str, content: str) -> str:
    """Add a comment to a process instance

    Args:
        process_instance_id: The ID of the process instance
        content: The comment content
    """
    await ensure_client_initialized()
    try:
        data = {"content": content}
        logger.info(f"Adding comment to process instance: {process_instance_id}")
        return await make_api_call_with_limits("POST", ENDPOINTS["process_instance_comments"],
                                             data=data, processInstanceId=process_instance_id)
    except Exception as e:
        logger.error(f"Error adding process instance comment: {str(e)}")
        return f"Error: {str(e)}"

# Task Management Tools
@mcp.tool()
async def get_tasks(assignee: Optional[str] = None, candidate_user: Optional[str] = None,
                   process_instance_id: Optional[str] = None, case_instance_id: Optional[str] = None) -> str:
    """Get tasks based on various filters

    Args:
        assignee: Filter by assignee user ID
        candidate_user: Filter by candidate user ID
        process_instance_id: Filter by process instance ID
        case_instance_id: Filter by case instance ID
    """
    await ensure_client_initialized()
    try:
        params = {}
        if assignee:
            params["assignee"] = assignee
        if candidate_user:
            params["candidateUser"] = candidate_user
        if process_instance_id:
            params["processInstanceId"] = process_instance_id
        if case_instance_id:
            params["caseInstanceId"] = case_instance_id

        logger.info("Getting tasks with filters")
        return await make_api_call_with_limits("GET", ENDPOINTS["tasks"], params=params)
    except Exception as e:
        logger.error(f"Error getting tasks: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_task(task_id: str) -> str:
    """Get details of a specific task

    Args:
        task_id: The ID of the task to retrieve
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting task: {task_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["task"], taskId=task_id)
    except Exception as e:
        logger.error(f"Error getting task: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def assign_task(task_id: str, assignee: str) -> str:
    """Assign a task to a user

    Args:
        task_id: The ID of the task
        assignee: The user ID to assign the task to
    """
    await ensure_client_initialized()
    try:
        data = {"assignee": assignee}
        logger.info(f"Assigning task {task_id} to {assignee}")
        return await make_api_call_with_limits("POST", ENDPOINTS["task_assign"],
                                             data=data, taskId=task_id)
    except Exception as e:
        logger.error(f"Error assigning task: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def claim_task(task_id: str, user_id: str) -> str:
    """Claim a task for a user

    Args:
        task_id: The ID of the task
        user_id: The user ID claiming the task
    """
    await ensure_client_initialized()
    try:
        data = {"assignee": user_id}
        logger.info(f"Claiming task {task_id} for user {user_id}")
        return await make_api_call_with_limits("POST", ENDPOINTS["task_claim"],
                                             data=data, taskId=task_id)
    except Exception as e:
        logger.error(f"Error claiming task: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def complete_task(task_id: str, variables: Optional[str] = None) -> str:
    """Complete a task

    Args:
        task_id: The ID of the task to complete
        variables: Optional JSON string of variables to set when completing the task
    """
    await ensure_client_initialized()
    try:
        data = {}
        if variables:
            try:
                data["variables"] = json.loads(variables)
            except json.JSONDecodeError:
                return "Error: Invalid JSON format for variables"

        logger.info(f"Completing task: {task_id}")
        return await make_api_call_with_limits("POST", ENDPOINTS["task_complete"],
                                             data=data, taskId=task_id)
    except Exception as e:
        logger.error(f"Error completing task: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_task_variables(task_id: str) -> str:
    """Get variables for a task

    Args:
        task_id: The ID of the task
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting variables for task: {task_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["task_variables"], taskId=task_id)
    except Exception as e:
        logger.error(f"Error getting task variables: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def set_task_variable(task_id: str, variable_name: str, variable_value: str,
                           variable_type: str = "string") -> str:
    """Set a variable on a task

    Args:
        task_id: The ID of the task
        variable_name: The name of the variable
        variable_value: The value of the variable
        variable_type: The type of the variable (string, integer, boolean, etc.)
    """
    await ensure_client_initialized()
    try:
        # Convert value based on type
        if variable_type == "integer":
            value = int(variable_value)
        elif variable_type == "boolean":
            value = variable_value.lower() in ("true", "1", "yes")
        elif variable_type == "json":
            value = json.loads(variable_value)
        else:
            value = variable_value

        data = {
            "name": variable_name,
            "type": variable_type,
            "value": value
        }

        logger.info(f"Setting variable {variable_name} on task: {task_id}")
        return await make_api_call_with_limits("PUT", ENDPOINTS["task_variable"],
                                             data=data, taskId=task_id, variableName=variable_name)
    except Exception as e:
        logger.error(f"Error setting task variable: {str(e)}")
        return f"Error: {str(e)}"

# Definition Management Tools
@mcp.tool()
async def get_process_definitions() -> str:
    """Get all process definitions"""
    await ensure_client_initialized()
    try:
        logger.info("Getting process definitions")
        return await make_api_call_with_limits("GET", ENDPOINTS["process_definitions"])
    except Exception as e:
        logger.error(f"Error getting process definitions: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_process_definition(process_definition_id: str) -> str:
    """Get details of a specific process definition

    Args:
        process_definition_id: The ID of the process definition
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting process definition: {process_definition_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["process_definition"],
                                             processDefinitionId=process_definition_id)
    except Exception as e:
        logger.error(f"Error getting process definition: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_case_definition_start_form(case_definition_id: str) -> str:
    """Get the start form for a case definition

    Args:
        case_definition_id: The ID of the case definition
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting start form for case definition: {case_definition_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["case_definition_start_form"],
                                             caseDefinitionId=case_definition_id)
    except Exception as e:
        logger.error(f"Error getting case definition start form: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_process_definition_start_form(process_definition_id: str) -> str:
    """Get the start form for a process definition

    Args:
        process_definition_id: The ID of the process definition
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting start form for process definition: {process_definition_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["process_definition_start_form"],
                                             processDefinitionId=process_definition_id)
    except Exception as e:
        logger.error(f"Error getting process definition start form: {str(e)}")
        return f"Error: {str(e)}"

# Audit and Monitoring Tools
@mcp.tool()
async def get_audit_trail(scope_id: Optional[str] = None, scope_type: Optional[str] = None,
                         type_filter: Optional[str] = None) -> str:
    """Get audit trail data

    Args:
        scope_id: Filter by scope ID (process/case instance ID)
        scope_type: Filter by scope type
        type_filter: Filter by audit entry type
    """
    await ensure_client_initialized()
    try:
        params = {}
        if scope_id:
            params["scopeId"] = scope_id
        if scope_type:
            params["scopeType"] = scope_type
        if type_filter:
            params["type"] = type_filter

        logger.info("Getting audit trail data")
        return await make_api_call_with_limits("GET", ENDPOINTS["audit_trail"], params=params)
    except Exception as e:
        logger.error(f"Error getting audit trail: {str(e)}")
        return f"Error: {str(e)}"

# Work Instance Tools
@mcp.tool()
async def get_work_instances(assignee: Optional[str] = None, type_filter: Optional[str] = None) -> str:
    """Get work instances

    Args:
        assignee: Filter by assignee user ID
        type_filter: Filter by work instance type
    """
    await ensure_client_initialized()
    try:
        params = {}
        if assignee:
            params["assignee"] = assignee
        if type_filter:
            params["type"] = type_filter

        logger.info("Getting work instances")
        return await make_api_call_with_limits("GET", ENDPOINTS["work_instances"], params=params)
    except Exception as e:
        logger.error(f"Error getting work instances: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def get_work_instance(work_instance_id: str) -> str:
    """Get details of a specific work instance

    Args:
        work_instance_id: The ID of the work instance
    """
    await ensure_client_initialized()
    try:
        logger.info(f"Getting work instance: {work_instance_id}")
        return await make_api_call_with_limits("GET", ENDPOINTS["work_instance"],
                                             workInstanceId=work_instance_id)
    except Exception as e:
        logger.error(f"Error getting work instance: {str(e)}")
        return f"Error: {str(e)}"

@mcp.tool()
async def assign_work_instance(work_instance_id: str, assignee: str) -> str:
    """Assign a work instance to a user

    Args:
        work_instance_id: The ID of the work instance
        assignee: The user ID to assign the work instance to
    """
    await ensure_client_initialized()
    try:
        data = {"assignee": assignee}
        logger.info(f"Assigning work instance {work_instance_id} to {assignee}")
        return await make_api_call_with_limits("POST", ENDPOINTS["work_instance_assign"],
                                             data=data, workInstanceId=work_instance_id)
    except Exception as e:
        logger.error(f"Error assigning work instance: {str(e)}")
        return f"Error: {str(e)}"

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(mcp, host="0.0.0.0", port=8000)
