"""Database configuration for Flowable MCP Server"""

import os
from typing import Optional

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./flowable_mcp.db")
DATABASE_ENABLED = os.getenv("DATABASE_ENABLED", "true").lower() == "true"

# Connection pool settings
DATABASE_POOL_SIZE = int(os.getenv("DATABASE_POOL_SIZE", "10"))
DATABASE_MAX_OVERFLOW = int(os.getenv("DATABASE_MAX_OVERFLOW", "20"))
DATABASE_POOL_TIMEOUT = int(os.getenv("DATABASE_POOL_TIMEOUT", "30"))
DATABASE_POOL_RECYCLE = int(os.getenv("DATABASE_POOL_RECYCLE", "3600"))

# Query settings
DATABASE_ECHO = os.getenv("DATABASE_ECHO", "false").lower() == "true"
DATABASE_ECHO_POOL = os.getenv("DATABASE_ECHO_POOL", "false").lower() == "true"

# Backup settings
BACKUP_ENABLED = os.getenv("BACKUP_ENABLED", "false").lower() == "true"
BACKUP_SCHEDULE = os.getenv("BACKUP_SCHEDULE", "0 2 * * *")  # Daily at 2 AM
BACKUP_RETENTION_DAYS = int(os.getenv("BACKUP_RETENTION_DAYS", "30"))
BACKUP_STORAGE_PATH = os.getenv("BACKUP_STORAGE_PATH", "./backups")

# Migration settings
AUTO_MIGRATE = os.getenv("AUTO_MIGRATE", "true").lower() == "true"
MIGRATION_TIMEOUT = int(os.getenv("MIGRATION_TIMEOUT", "300"))

# Logging settings
LOG_DATABASE_QUERIES = os.getenv("LOG_DATABASE_QUERIES", "false").lower() == "true"
LOG_SLOW_QUERIES = os.getenv("LOG_SLOW_QUERIES", "true").lower() == "true"
SLOW_QUERY_THRESHOLD = float(os.getenv("SLOW_QUERY_THRESHOLD", "1.0"))  # seconds

# Performance settings
BATCH_SIZE = int(os.getenv("DATABASE_BATCH_SIZE", "1000"))
MAX_RETRIES = int(os.getenv("DATABASE_MAX_RETRIES", "3"))
RETRY_DELAY = float(os.getenv("DATABASE_RETRY_DELAY", "1.0"))  # seconds

# Security settings
ENCRYPT_SENSITIVE_DATA = os.getenv("ENCRYPT_SENSITIVE_DATA", "false").lower() == "true"
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY")  # Should be set in production

# Health check settings
HEALTH_CHECK_TIMEOUT = int(os.getenv("DATABASE_HEALTH_CHECK_TIMEOUT", "5"))
HEALTH_CHECK_INTERVAL = int(os.getenv("DATABASE_HEALTH_CHECK_INTERVAL", "30"))

# Cleanup settings
AUTO_CLEANUP_ENABLED = os.getenv("AUTO_CLEANUP_ENABLED", "true").lower() == "true"
CLEANUP_OLDER_THAN_DAYS = int(os.getenv("CLEANUP_OLDER_THAN_DAYS", "90"))
CLEANUP_SCHEDULE = os.getenv("CLEANUP_SCHEDULE", "0 3 * * 0")  # Weekly on Sunday at 3 AM
