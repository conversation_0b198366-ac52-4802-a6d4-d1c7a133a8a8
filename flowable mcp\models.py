"""Data models for Flowable MCP Server"""

from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class APIError(Exception):
    """Custom exception for API errors"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict[str, Any]] = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(self.message)


class FlowableResponse(BaseModel):
    """Standard response model for Flowable API calls"""
    success: bool = Field(description="Whether the API call was successful")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Response data from the API")
    status_code: Optional[int] = Field(default=None, description="HTTP status code")
    message: Optional[str] = Field(default=None, description="Response message")
    error: Optional[str] = Field(default=None, description="Error message if any")


class AuthType(str, Enum):
    """Authentication types supported by Flowable"""
    BASIC = "basic"
    TOKEN = "token"


class InstanceState(str, Enum):
    """Instance states in Flowable"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    COMPLETED = "completed"
    TERMINATED = "terminated"


class TaskState(str, Enum):
    """Task states in Flowable"""
    CREATED = "created"
    ASSIGNED = "assigned"
    COMPLETED = "completed"
    SUSPENDED = "suspended"


# Case Instance Models
class CaseInstanceRepresentation(BaseModel):
    """Representation of a case instance"""
    id: Optional[str] = None
    name: Optional[str] = None
    business_key: Optional[str] = None
    business_status: Optional[str] = None
    case_definition_id: Optional[str] = None
    case_definition_key: Optional[str] = None
    case_definition_name: Optional[str] = None
    case_definition_category: Optional[str] = None
    case_definition_version: Optional[int] = None
    case_definition_deployment_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    start_user_id: Optional[str] = None
    callback_id: Optional[str] = None
    callback_type: Optional[str] = None
    tenant_id: Optional[str] = None
    state: Optional[str] = None
    parent_id: Optional[str] = None
    assignee: Optional[str] = None
    owner: Optional[str] = None


class CreateCaseInstanceRepresentation(BaseModel):
    """Request model for creating a case instance"""
    case_definition_id: Optional[str] = None
    case_definition_key: Optional[str] = None
    name: Optional[str] = None
    business_key: Optional[str] = None
    business_status: Optional[str] = None
    variables: Optional[Dict[str, Any]] = None
    tenant_id: Optional[str] = None
    outcome: Optional[str] = None
    callback_id: Optional[str] = None
    callback_type: Optional[str] = None
    parent_deployment_id: Optional[str] = None
    fallback_to_default_tenant: Optional[bool] = None


# Process Instance Models
class ProcessInstanceRepresentation(BaseModel):
    """Representation of a process instance"""
    id: Optional[str] = None
    name: Optional[str] = None
    business_key: Optional[str] = None
    business_status: Optional[str] = None
    process_definition_id: Optional[str] = None
    process_definition_key: Optional[str] = None
    process_definition_name: Optional[str] = None
    process_definition_category: Optional[str] = None
    process_definition_version: Optional[int] = None
    process_definition_deployment_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    start_user_id: Optional[str] = None
    start_activity_id: Optional[str] = None
    delete_reason: Optional[str] = None
    super_process_instance_id: Optional[str] = None
    tenant_id: Optional[str] = None
    callback_id: Optional[str] = None
    callback_type: Optional[str] = None


class CreateProcessInstanceRepresentation(BaseModel):
    """Request model for creating a process instance"""
    process_definition_id: Optional[str] = None
    process_definition_key: Optional[str] = None
    name: Optional[str] = None
    business_key: Optional[str] = None
    business_status: Optional[str] = None
    variables: Optional[Dict[str, Any]] = None
    tenant_id: Optional[str] = None
    outcome: Optional[str] = None
    callback_id: Optional[str] = None
    callback_type: Optional[str] = None
    parent_deployment_id: Optional[str] = None
    fallback_to_default_tenant: Optional[bool] = None


# Task Models
class TaskRepresentation(BaseModel):
    """Representation of a task"""
    id: Optional[str] = None
    owner: Optional[str] = None
    assignee: Optional[str] = None
    delegation_state: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None
    create_time: Optional[datetime] = None
    due_date: Optional[datetime] = None
    priority: Optional[int] = None
    suspended: Optional[bool] = None
    claim_time: Optional[datetime] = None
    task_definition_key: Optional[str] = None
    scope_definition_id: Optional[str] = None
    scope_id: Optional[str] = None
    sub_scope_id: Optional[str] = None
    scope_type: Optional[str] = None
    tenant_id: Optional[str] = None
    category: Optional[str] = None
    form_key: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    parent_task_id: Optional[str] = None
    parent_task_name: Optional[str] = None
    execution_id: Optional[str] = None
    process_instance_id: Optional[str] = None
    process_definition_id: Optional[str] = None
    process_definition_name: Optional[str] = None
    process_definition_category: Optional[str] = None
    process_instance_name: Optional[str] = None
    case_definition_name: Optional[str] = None
    case_definition_category: Optional[str] = None
    case_instance_name: Optional[str] = None
    task_definition_id: Optional[str] = None
    permissions: Optional[List[str]] = None
    state: Optional[str] = None
    completed_by: Optional[str] = None
    in_progress_start_time: Optional[datetime] = None
    in_progress_started_by: Optional[str] = None
    in_progress_start_due_date: Optional[datetime] = None


# Variable Models
class RestVariable(BaseModel):
    """Representation of a variable"""
    name: str
    type: Optional[str] = None
    value: Optional[Any] = None
    value_url: Optional[str] = None
    scope: Optional[str] = None


# Comment Models
class CommentInstanceRepresentation(BaseModel):
    """Representation of a comment"""
    id: Optional[str] = None
    content: Optional[str] = None
    user_id: Optional[str] = None
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    scope_id: Optional[str] = None
    scope_type: Optional[str] = None


class CreateCommentRequest(BaseModel):
    """Request model for creating a comment"""
    content: str


class UpdateCommentRequest(BaseModel):
    """Request model for updating a comment"""
    content: str


# Assignment Models
class ChangeAssigneeRequest(BaseModel):
    """Request model for changing assignee"""
    assignee: Optional[str] = None


# Query Models
class DataResponse(BaseModel):
    """Generic data response model"""
    data: List[Dict[str, Any]]
    total: Optional[int] = None
    start: Optional[int] = None
    sort: Optional[str] = None
    order: Optional[str] = None
    size: Optional[int] = None


# Form Models
class FormModelRepresentation(BaseModel):
    """Representation of a form model"""
    id: Optional[str] = None
    name: Optional[str] = None
    key: Optional[str] = None
    version: Optional[int] = None
    description: Optional[str] = None
    fields: Optional[List[Dict[str, Any]]] = None
    outcomes: Optional[List[Dict[str, Any]]] = None


# Content Models
class ContentItemRepresentation(BaseModel):
    """Representation of a content item"""
    id: Optional[str] = None
    name: Optional[str] = None
    mime_type: Optional[str] = None
    task_id: Optional[str] = None
    process_instance_id: Optional[str] = None
    content_store_id: Optional[str] = None
    content_store_name: Optional[str] = None
    created: Optional[datetime] = None
    created_by: Optional[str] = None
    last_modified: Optional[datetime] = None
    last_modified_by: Optional[str] = None
    content_size: Optional[int] = None
    tenant_id: Optional[str] = None
    content_available: Optional[bool] = None
    field: Optional[str] = None
    related_content: Optional[bool] = None
    link: Optional[bool] = None
    link_url: Optional[str] = None
    simple_type: Optional[str] = None
    preview_status: Optional[str] = None
    thumbnail_status: Optional[str] = None


# Audit Models
class AuditEntry(BaseModel):
    """Representation of an audit entry"""
    id: Optional[str] = None
    type: Optional[str] = None
    sub_type: Optional[str] = None
    scope_id: Optional[str] = None
    scope_type: Optional[str] = None
    scope_definition_id: Optional[str] = None
    sub_scope_id: Optional[str] = None
    external_id: Optional[str] = None
    creator_id: Optional[str] = None
    create_time: Optional[datetime] = None
    definition_key: Optional[str] = None
    payload: Optional[Dict[str, Any]] = None


# Deployment Models
class DeploymentRepresentation(BaseModel):
    """Representation of a deployment"""
    id: Optional[str] = None
    name: Optional[str] = None
    deployment_time: Optional[datetime] = None
    category: Optional[str] = None
    tenant_id: Optional[str] = None
    parent_deployment_id: Optional[str] = None


# Definition Models
class DefinitionRepresentation(BaseModel):
    """Base representation of a definition"""
    id: Optional[str] = None
    key: Optional[str] = None
    name: Optional[str] = None
    version: Optional[int] = None
    category: Optional[str] = None
    description: Optional[str] = None
    deployment_id: Optional[str] = None
    tenant_id: Optional[str] = None
    resource_name: Optional[str] = None
    diagram_resource_name: Optional[str] = None
    has_start_form_key: Optional[bool] = None
    has_graphical_notation: Optional[bool] = None
    suspended: Optional[bool] = None
    start_form_defined: Optional[bool] = None


# Work Instance Models
class WorkInstanceRepresentation(BaseModel):
    """Representation of a work instance"""
    id: Optional[str] = None
    name: Optional[str] = None
    business_key: Optional[str] = None
    business_status: Optional[str] = None
    tenant_id: Optional[str] = None
    type: Optional[str] = None
    definition_id: Optional[str] = None
    definition_name: Optional[str] = None
    definition_key: Optional[str] = None
    definition_version: Optional[int] = None
    definition_deployment_id: Optional[str] = None
    definition_category: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    start_user_id: Optional[str] = None
    assignee: Optional[str] = None
    owner: Optional[str] = None
    root_scope_id: Optional[str] = None
    root_scope_type: Optional[str] = None
    parent_scope_id: Optional[str] = None
    parent_scope_type: Optional[str] = None
