version: '3.8'

services:
  flowable-mcp:
    build: .
    ports:
      - "8000:8000"
    environment:
      - FLOWABLE_BASE_URL=${FLOWABLE_BASE_URL:-http://localhost:8090/platform-api}
      - FLOWABLE_USERNAME=${FLOWABLE_USERNAME}
      - FLOWABLE_PASSWORD=${FLOWABLE_PASSWORD}
      - FLOWABLE_API_TOKEN=${FLOWABLE_API_TOKEN}
      - DATABASE_URL=${DATABASE_URL:-sqlite+aiosqlite:///./data/flowable_mcp.db}
      - DATABASE_ENABLED=${DATABASE_ENABLED:-true}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - flowable-network

  # Optional: PostgreSQL database for production use
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=flowable_mcp
      - POSTGRES_USER=flowable
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-flowable123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - flowable-network
    profiles:
      - postgres

  # Optional: Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - flowable-network
    profiles:
      - redis

  # Optional: Nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - flowable-mcp
    restart: unless-stopped
    networks:
      - flowable-network
    profiles:
      - nginx

volumes:
  postgres_data:
  redis_data:

networks:
  flowable-network:
    driver: bridge
