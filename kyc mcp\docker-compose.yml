version: '3.8'

services:
  # KYC MCP Server
  kyc-server:
    build: .
    container_name: kyc-mcp-server
    ports:
      - "8000:8000"
    # Load environment file
    env_file:
      - .env.docker
    environment:
      - PORT=8000
      - HOST=0.0.0.0
      - PYTHONUNBUFFERED=1
    volumes:
      # Core application files
      - ./kyc_http_server.py:/app/kyc_http_server.py:ro
      - ./enhanced_langchain_agent.py:/app/enhanced_langchain_agent.py:ro
      - ./kyc_client.py:/app/kyc_client.py:ro
      - ./config.py:/app/config.py:ro
      - ./config_db.py:/app/config_db.py:ro
      - ./database.py:/app/database.py:ro
      - ./database_models.py:/app/database_models.py:ro
      - ./universal_database.py:/app/universal_database.py:ro
      - ./models.py:/app/models.py:ro
      - ./mysql_config.py:/app/mysql_config.py:ro
      
      # Google Sheets integration files
      - ./google_config.py:/app/google_config.py:ro
      - ./google_sheets_database.py:/app/google_sheets_database.py:ro
      - ./universal_google_sheets.py:/app/universal_google_sheets.py:ro
      - ./google_drive_storage.py:/app/google_drive_storage.py:ro
      
      # Google credentials file (create this file on your server)
      - ./credentials.json:/app/credentials.json:ro
      
      # Data volume
      - kyc_data:/app/data
    networks:
      - kyc_network
      - default
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  kyc_data:
    driver: local

networks:
  kyc_network:
    driver: bridge